// Production API Routes for Template Management
// Multi-tenant, RBAC-secured endpoints for prompt templates

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/database";
import { verifyToken, hasPermission } from "@/lib/auth-context";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";

interface CreateTemplateRequest {
  name: string;
  description: string;
  content: string;
  category: string;
  tags: string[];
  variables: Array<{
    name: string;
    type: string;
    description: string;
    required: boolean;
    defaultValue?: any;
  }>;
  isPublic: boolean;
  version: string;
}

interface ListTemplatesQuery {
  page?: string;
  limit?: string;
  category?: string;
  isPublic?: string;
  userId?: string;
}

// GET /api/templates - List templates with multi-tenant filtering
export async function GET(request: NextRequest) {
  try {
    // Extract and verify JWT token
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    // Check permissions
    if (!hasPermission(payload.role, payload.permissions, "templates", "read")) {
      return NextResponse.json(
        { error: "Insufficient permissions to read templates" },
        { status: 403 },
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const query: ListTemplatesQuery = {
      page: searchParams.get("page") || "1",
      limit: searchParams.get("limit") || "20",
      category: searchParams.get("category") || undefined,
      isPublic: searchParams.get("isPublic") || undefined,
      userId: searchParams.get("userId") || undefined,
    };

    const page = parseInt(query.page ?? "1");  
    const limit = Math.min(parseInt(query.limit ?? "20"), 100); // Max 100 per page
    const offset = (page - 1) * limit;

    // Build filters with organization scoping
    const filters = {
      limit,
      offset,
      ...(query.category && { category: query.category }),
      ...(query.isPublic !== undefined && {
        isPublic: query.isPublic === "true",
      }),
      ...(query.userId && { userId: query.userId }),
    };

    // Fetch templates with multi-tenant security
    const { templates, total } = await db.listTemplates(payload.org, filters);

    // Create session for this API call
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "templates",
      "user",
      { action: "list", filters, results: templates.length },
      {
        tags: ["api", "templates", "list"],
        conversationId: `api-${Date.now()}`,
      },
    );

    // Send real-time event via APIX
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      apixClient.sendEvent({
        id: `templates-listed-${Date.now()}`,
        type: "templates_listed",
        module: "templates",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          count: templates.length,
          total,
          filters,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json({
      success: true,
      data: {
        templates,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        sessionId,
      },
    });
  } catch (error) {
    console.error("Error listing templates:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// POST /api/templates - Create new template
export async function POST(request: NextRequest) {
  try {
    // Extract and verify JWT token
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    // Check permissions
    if (!hasPermission(payload.role, payload.permissions, "templates", "create")) {
      return NextResponse.json(
        { error: "Insufficient permissions to create templates" },
        { status: 403 },
      );
    }

    // Parse and validate request body
    const body: CreateTemplateRequest = await request.json();

    // Validation
    if (!body.name?.trim()) {
      return NextResponse.json(
        { error: "Template name is required" },
        { status: 400 },
      );
    }

    if (!body.content?.trim()) {
      return NextResponse.json(
        { error: "Template content is required" },
        { status: 400 },
      );
    }

    // Create template with organization scoping
    const templateData = {
      organizationId: payload.org,
      userId: payload.sub,
      name: body.name.trim(),
      description: body.description?.trim() || "",
      content: body.content.trim(),
      category: body.category || "general",
      tags: body.tags || [],
      variables: body.variables || [],
      isPublic: body.isPublic || false,
      version: body.version || "1.0.0",
    };

    const template = await db.createTemplate(templateData);

    // Create session for this creation
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "templates",
      "user",
      { action: "create", templateId: template.id, templateData },
      {
        tags: ["api", "templates", "create"],
        conversationId: `api-create-${Date.now()}`,
      },
    );

    // Send real-time event via APIX
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      apixClient.sendEvent({
        id: `template-created-${template.id}`,
        type: "created",
        module: "templates",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          templateId: template.id,
          template,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json(
      {
        success: true,
        data: {
          template,
          sessionId,
        },
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("Error creating template:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
