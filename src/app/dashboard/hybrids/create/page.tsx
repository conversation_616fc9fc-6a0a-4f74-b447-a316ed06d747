"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Save,
  Play,
  Share,
  Settings,
  Shield,
  Wifi,
  WifiOff,
  AlertCircle,
  CheckCircle,
  Clock,
  Network,
  Bot,
  Wrench,
  Diamond,
} from "lucide-react";

import FlowWorkflowBuilder from "@/components/hybrid-system/FlowWorkflowBuilder";
import { mockAuthContext } from "@/lib/auth-context";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";

interface WorkflowData {
  id: string;
  name: string;
  description: string;
  nodes: any[];
  edges: any[];
  settings: {
    timeout: number;
    retries: number;
    parallelism: number;
    errorHandling: "stop" | "continue" | "retry";
    logging: boolean;
    monitoring: boolean;
  };
  version: string;
  isActive: boolean;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  executionCount: number;
  successRate: number;
  avgExecutionTime: number;
  totalCost: number;
}

export default function CreateHybridWorkflowPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const workflowId = searchParams.get("id");
  const mode = searchParams.get("mode") || "create";

  // State
  const [workflowData, setWorkflowData] = useState<WorkflowData | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Auth & RBAC
  const { user, organization, hasPermission } = mockAuthContext;
  const canCreateWorkflow = hasPermission("hybrids", "create");
  const canEditWorkflow = hasPermission("hybrids", "update");
  const canExecuteWorkflow = hasPermission("hybrids", "execute");
  const canShareWorkflow = hasPermission("hybrids", "share");

  // Initialize connection and load workflow data
  useEffect(() => {
    const initializeConnection = async () => {
      try {
        const apixClient = getAPXClient(
          organization?.id || "org-123",
          user?.id || "user-456",
          "mock-token",
        );
        await apixClient.connect();
        setIsConnected(true);

        // Load existing workflow if editing
        if (workflowId && mode === "edit") {
          await loadWorkflow(workflowId);
        }
      } catch (error) {
        console.error("Failed to initialize connection:", error);
        setIsConnected(false);
        setError("Failed to connect to SynapseAI. Please refresh the page.");
      }
    };

    initializeConnection();
  }, [workflowId, mode, organization?.id, user?.id]);

  const loadWorkflow = async (id: string) => {
    try {
      // Mock loading workflow data - replace with actual API call
      const mockWorkflow: WorkflowData = {
        id,
        name: "Sample Workflow",
        description: "A sample hybrid workflow for demonstration",
        nodes: [],
        edges: [],
        settings: {
          timeout: 300000,
          retries: 3,
          parallelism: 1,
          errorHandling: "stop",
          logging: true,
          monitoring: true,
        },
        version: "1.0.0",
        isActive: false,
        isPublic: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: user?.id || "unknown",
        executionCount: 0,
        successRate: 0,
        avgExecutionTime: 0,
        totalCost: 0,
      };
      setWorkflowData(mockWorkflow);
    } catch (error) {
      console.error("Failed to load workflow:", error);
      setError("Failed to load workflow data.");
    }
  };

  const handleSave = async (data: WorkflowData) => {
    if (!canCreateWorkflow && !canEditWorkflow) {
      setError("You do not have permission to save workflows.");
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      // Mock save operation - replace with actual API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setWorkflowData(data);
      setLastSaved(new Date());
      setSuccess("Workflow saved successfully!");

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Failed to save workflow:", error);
      setError("Failed to save workflow. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleExecute = async (data: WorkflowData, input?: any) => {
    if (!canExecuteWorkflow) {
      setError("You do not have permission to execute workflows.");
      return;
    }

    setIsExecuting(true);
    setError(null);

    try {
      // Mock execution - replace with actual API call
      await new Promise((resolve) => setTimeout(resolve, 2000));
      setSuccess("Workflow executed successfully!");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Failed to execute workflow:", error);
      setError("Failed to execute workflow. Please try again.");
    } finally {
      setIsExecuting(false);
    }
  };

  const handleShare = () => {
    if (!canShareWorkflow) {
      setError("You do not have permission to share workflows.");
      return;
    }
    // Implement sharing logic
    setSuccess("Workflow sharing link copied to clipboard!");
    setTimeout(() => setSuccess(null), 3000);
  };

  const getPageTitle = () => {
    switch (mode) {
      case "edit":
        return "Edit Hybrid Workflow";
      case "view":
        return "View Hybrid Workflow";
      case "debug":
        return "Debug Hybrid Workflow";
      default:
        return "Create New Hybrid Workflow";
    }
  };

  const getPageDescription = () => {
    switch (mode) {
      case "edit":
        return "Modify your existing hybrid workflow configuration";
      case "view":
        return "View workflow details and execution history";
      case "debug":
        return "Debug and test your workflow step by step";
      default:
        return "Build intelligent workflows combining AI agents and tools";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push("/dashboard/hybrids")}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Hybrids
              </Button>
              <Separator orientation="vertical" className="h-6" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {getPageTitle()}
                </h1>
                <p className="text-sm text-gray-600 mt-1">
                  {getPageDescription()}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {/* Connection Status */}
              <Badge variant={isConnected ? "default" : "destructive"}>
                {isConnected ? (
                  <Wifi className="h-3 w-3 mr-1" />
                ) : (
                  <WifiOff className="h-3 w-3 mr-1" />
                )}
                {isConnected ? "Connected" : "Disconnected"}
              </Badge>

              {/* User Role */}
              <Badge variant="outline" className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                {user?.role || "User"}
              </Badge>

              {/* Last Saved */}
              {lastSaved && (
                <div className="text-sm text-gray-500 flex items-center">
                  <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                  Saved {lastSaved.toLocaleTimeString()}
                </div>
              )}

              {/* Action Buttons */}
              {mode !== "view" && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleShare}
                  disabled={!canShareWorkflow}
                >
                  <Share className="h-4 w-4 mr-2" />
                  Share
                </Button>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  router.push(
                    `/dashboard/hybrids/create?id=${workflowId}&mode=debug`,
                  )
                }
              >
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Alerts */}
      <div className="px-6 py-4 space-y-3">
        {!isConnected && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Connection to SynapseAI lost. Some features may not work properly.
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}
      </div>

      {/* Main Content */}
      <div className="px-6 pb-6">
        <Card className="bg-white">
          <CardContent className="p-0">
            <FlowWorkflowBuilder
              workflowId={workflowId || undefined}
              initialData={workflowData || undefined}
              mode={mode as "create" | "edit" | "view" | "debug"}
              onSave={handleSave}
              onExecute={handleExecute}
              className="h-[calc(100vh-200px)]"
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
