"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  Play,
  Palette,
  Zap,
  Settings,
  Network,
  Bot,
  Wrench,
  Diamond,
  User,
  CheckCircle,
  Star,
  Sparkles,
  Code,
  Eye,
  Download,
  Share,
} from 'lucide-react';

import FlowWorkflowBuilder from '@/components/hybrid-system/FlowWorkflowBuilder';
import ProfessionalShowcase from '@/components/hybrid-system/ProfessionalShowcase';

export default function HybridDemoPage() {
  const router = useRouter();
  const [activeDemo, setActiveDemo] = useState('professional');

  const demoWorkflows = {
    professional: {
      name: "Professional Business Process",
      description: "A complete business workflow with approval gates and automation",
      nodes: [
        {
          id: 'start',
          type: 'start',
          position: { x: 100, y: 200 },
          data: { name: 'Start Process' },
        },
        {
          id: 'agent-1',
          type: 'agent',
          position: { x: 300, y: 150 },
          data: { 
            name: 'Document Analyzer',
            description: 'AI agent that analyzes incoming documents',
            status: 'completed',
            config: {
              provider: 'OpenAI GPT-4',
              temperature: 0.3,
              systemPrompt: 'Analyze documents for compliance and quality'
            }
          },
        },
        {
          id: 'condition-1',
          type: 'condition',
          position: { x: 500, y: 150 },
          data: { 
            name: 'Quality Check',
            description: 'Evaluate document quality score',
            status: 'completed',
            config: {
              condition: 'quality_score > 0.8',
              operator: 'greater_than'
            }
          },
        },
        {
          id: 'hitl-1',
          type: 'hitl',
          position: { x: 700, y: 100 },
          data: { 
            name: 'Manager Approval',
            description: 'Requires manager approval for high-value documents',
            status: 'pending_approval',
            config: {
              approvers: ['<EMAIL>'],
              timeout: 60,
              formFields: [
                { name: 'priority', type: 'select', required: true, options: ['Low', 'Medium', 'High'] },
                { name: 'comments', type: 'text', required: false }
              ]
            },
            approvalData: {
              requestedAt: new Date().toISOString()
            }
          },
        },
        {
          id: 'tool-1',
          type: 'tool',
          position: { x: 700, y: 250 },
          data: { 
            name: 'Auto Processor',
            description: 'Automatically processes low-risk documents',
            status: 'idle',
            config: {
              endpoint: '/api/process-document',
              method: 'POST',
              timeout: 30000
            }
          },
        },
        {
          id: 'agent-2',
          type: 'agent',
          position: { x: 900, y: 175 },
          data: { 
            name: 'Final Reviewer',
            description: 'AI agent that performs final review and generates summary',
            status: 'idle',
            config: {
              provider: 'Claude 3 Sonnet',
              temperature: 0.1,
              systemPrompt: 'Perform final review and generate executive summary'
            }
          },
        },
        {
          id: 'end',
          type: 'end',
          position: { x: 1100, y: 200 },
          data: { name: 'Process Complete' },
        },
      ],
      edges: [
        { id: 'e1', source: 'start', target: 'agent-1', type: 'custom' },
        { id: 'e2', source: 'agent-1', target: 'condition-1', type: 'custom' },
        { id: 'e3', source: 'condition-1', target: 'hitl-1', sourceHandle: 'yes', type: 'custom', data: { label: 'High Quality' } },
        { id: 'e4', source: 'condition-1', target: 'tool-1', sourceHandle: 'no', type: 'custom', data: { label: 'Standard Quality' } },
        { id: 'e5', source: 'hitl-1', target: 'agent-2', sourceHandle: 'approved', type: 'custom', data: { label: 'Approved' } },
        { id: 'e6', source: 'tool-1', target: 'agent-2', type: 'custom' },
        { id: 'e7', source: 'agent-2', target: 'end', type: 'custom' },
      ]
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <div className="bg-white border-b shadow-sm">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/dashboard/hybrids')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Hybrids
              </Button>
              <Separator orientation="vertical" className="h-6" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                  <Sparkles className="h-6 w-6 text-blue-500" />
                  Professional Workflow Builder Demo
                </h1>
                <p className="text-sm text-gray-600 mt-1">
                  Experience the full power of SynapseAI's hybrid workflow system
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Badge variant="default" className="bg-gradient-to-r from-blue-500 to-purple-500">
                <Star className="h-3 w-3 mr-1" />
                Professional Edition
              </Badge>
              <Button 
                onClick={() => router.push('/dashboard/hybrids/create')}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <Play className="h-4 w-4 mr-2" />
                Try It Now
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="px-6 py-6 space-y-6">
        {/* Professional Showcase */}
        <ProfessionalShowcase />

        {/* Live Demo */}
        <Card className="bg-white shadow-lg">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5 text-blue-500" />
                  Live Interactive Demo
                </CardTitle>
                <CardDescription>
                  Explore a real business workflow with all node types and features
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline" size="sm">
                  <Share className="h-4 w-4 mr-2" />
                  Share
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="h-[600px] border-t">
              <FlowWorkflowBuilder
                mode="view"
                initialData={{
                  id: 'demo-workflow',
                  name: demoWorkflows.professional.name,
                  description: demoWorkflows.professional.description,
                  nodes: demoWorkflows.professional.nodes,
                  edges: demoWorkflows.professional.edges,
                  settings: {
                    timeout: 300000,
                    retries: 3,
                    parallelism: 1,
                    errorHandling: "stop",
                    logging: true,
                    monitoring: true,
                  },
                  version: "1.0.0",
                  isActive: true,
                  isPublic: false,
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                  createdBy: "demo-user",
                  executionCount: 42,
                  successRate: 94.2,
                  avgExecutionTime: 2400,
                  totalCost: 0.15,
                }}
              />
            </div>
          </CardContent>
        </Card>

        {/* Node Types Showcase */}
        <Card className="bg-white shadow-lg">
          <CardHeader>
            <CardTitle>Node Types Showcase</CardTitle>
            <CardDescription>
              Professional node components with rich functionality
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="p-4 border rounded-lg text-center">
                <Bot className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                <h4 className="font-medium">AI Agent</h4>
                <p className="text-xs text-gray-500">Intelligent reasoning</p>
              </div>
              <div className="p-4 border rounded-lg text-center">
                <Wrench className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <h4 className="font-medium">Tool</h4>
                <p className="text-xs text-gray-500">Functional tasks</p>
              </div>
              <div className="p-4 border rounded-lg text-center">
                <Diamond className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                <h4 className="font-medium">Condition</h4>
                <p className="text-xs text-gray-500">Logic gates</p>
              </div>
              <div className="p-4 border rounded-lg text-center">
                <User className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                <h4 className="font-medium">HITL</h4>
                <p className="text-xs text-gray-500">Human approval</p>
              </div>
              <div className="p-4 border rounded-lg text-center">
                <Play className="h-8 w-8 text-gray-500 mx-auto mb-2" />
                <h4 className="font-medium">Start/End</h4>
                <p className="text-xs text-gray-500">Flow control</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg">
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-4">Ready to Build Your Own Workflows?</h2>
            <p className="text-blue-100 mb-6">
              Start creating powerful AI-driven workflows with our professional builder
            </p>
            <div className="flex items-center justify-center gap-4">
              <Button 
                onClick={() => router.push('/dashboard/hybrids/create')}
                className="bg-white text-blue-600 hover:bg-gray-100"
              >
                <Play className="h-4 w-4 mr-2" />
                Create Workflow
              </Button>
              <Button 
                onClick={() => router.push('/dashboard/hybrids/docs')}
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600"
              >
                View Documentation
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
