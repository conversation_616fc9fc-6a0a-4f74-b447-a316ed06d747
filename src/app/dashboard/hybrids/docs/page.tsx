"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  Bot,
  Wrench,
  Diamond,
  Network,
  Play,
  Settings,
  Code,
  BookOpen,
  Lightbulb,
  Zap,
  Shield,
  Globe,
} from 'lucide-react';

export default function HybridDocsPage() {
  const router = useRouter();

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/dashboard/hybrids')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Hybrids
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Hybrid System Documentation
            </h1>
            <p className="text-gray-600 mt-1">
              Learn how to build powerful AI workflows with SynapseAI
            </p>
          </div>
        </div>
        <Button 
          onClick={() => router.push('/dashboard/hybrids/create')}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Play className="h-4 w-4 mr-2" />
          Try It Now
        </Button>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="nodes">Node Types</TabsTrigger>
          <TabsTrigger value="workflows">Workflows</TabsTrigger>
          <TabsTrigger value="examples">Examples</TabsTrigger>
          <TabsTrigger value="api">API Reference</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="h-5 w-5 mr-2" />
                What are Hybrid Workflows?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-700">
                Hybrid workflows in SynapseAI combine the intelligence of AI agents with the functionality 
                of tools to create powerful, automated processes. They allow you to build complex workflows 
                that can reason, make decisions, and perform actions.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center mb-2">
                    <Bot className="h-5 w-5 text-blue-500 mr-2" />
                    <h3 className="font-semibold">AI Agents</h3>
                  </div>
                  <p className="text-sm text-gray-600">
                    Intelligent reasoning and decision-making powered by large language models
                  </p>
                </div>
                
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center mb-2">
                    <Wrench className="h-5 w-5 text-green-500 mr-2" />
                    <h3 className="font-semibold">Tools</h3>
                  </div>
                  <p className="text-sm text-gray-600">
                    Functional components that perform specific tasks like API calls, data processing
                  </p>
                </div>
                
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center mb-2">
                    <Diamond className="h-5 w-5 text-yellow-500 mr-2" />
                    <h3 className="font-semibold">Conditions</h3>
                  </div>
                  <p className="text-sm text-gray-600">
                    Logic gates that control workflow flow based on data or AI decisions
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lightbulb className="h-5 w-5 mr-2" />
                Key Benefits
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <Zap className="h-5 w-5 text-yellow-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">Intelligent Automation</h4>
                      <p className="text-sm text-gray-600">
                        Combine AI reasoning with automated actions for smart workflows
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <Shield className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">Reliable Execution</h4>
                      <p className="text-sm text-gray-600">
                        Built-in error handling, retries, and monitoring
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <Network className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">Visual Builder</h4>
                      <p className="text-sm text-gray-600">
                        Drag-and-drop interface for creating complex workflows
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <Globe className="h-5 w-5 text-purple-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">Multi-Provider</h4>
                      <p className="text-sm text-gray-600">
                        Support for OpenAI, Claude, Gemini, and more
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Node Types Tab */}
        <TabsContent value="nodes" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bot className="h-5 w-5 text-blue-500 mr-2" />
                  AI Agent Node
                </CardTitle>
                <CardDescription>
                  Intelligent reasoning and decision-making
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <h4 className="font-medium mb-2">Capabilities:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Natural language processing</li>
                    <li>• Context-aware responses</li>
                    <li>• Memory and state management</li>
                    <li>• Multi-turn conversations</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Configuration:</h4>
                  <div className="space-y-1">
                    <Badge variant="outline">System Prompt</Badge>
                    <Badge variant="outline">Temperature</Badge>
                    <Badge variant="outline">Max Tokens</Badge>
                    <Badge variant="outline">Provider Selection</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Wrench className="h-5 w-5 text-green-500 mr-2" />
                  Tool Node
                </CardTitle>
                <CardDescription>
                  Functional components for specific tasks
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <h4 className="font-medium mb-2">Types:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• API calls and webhooks</li>
                    <li>• Data transformation</li>
                    <li>• File operations</li>
                    <li>• Database queries</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Features:</h4>
                  <div className="space-y-1">
                    <Badge variant="outline">Input Validation</Badge>
                    <Badge variant="outline">Error Handling</Badge>
                    <Badge variant="outline">Retry Logic</Badge>
                    <Badge variant="outline">Output Mapping</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Diamond className="h-5 w-5 text-yellow-500 mr-2" />
                  Condition Node
                </CardTitle>
                <CardDescription>
                  Logic gates for workflow control
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <h4 className="font-medium mb-2">Logic Types:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• If/Then/Else conditions</li>
                    <li>• Data comparisons</li>
                    <li>• Pattern matching</li>
                    <li>• AI-based decisions</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Operators:</h4>
                  <div className="space-y-1">
                    <Badge variant="outline">Equals</Badge>
                    <Badge variant="outline">Contains</Badge>
                    <Badge variant="outline">Greater Than</Badge>
                    <Badge variant="outline">Custom Logic</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Workflows Tab */}
        <TabsContent value="workflows" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Building Your First Workflow</CardTitle>
              <CardDescription>
                Step-by-step guide to creating a hybrid workflow
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    1
                  </div>
                  <div>
                    <h4 className="font-medium">Start with a Goal</h4>
                    <p className="text-sm text-gray-600">
                      Define what you want your workflow to accomplish. Examples: content generation, 
                      data processing, customer support automation.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    2
                  </div>
                  <div>
                    <h4 className="font-medium">Add Nodes</h4>
                    <p className="text-sm text-gray-600">
                      Drag and drop AI agents, tools, and condition nodes onto the canvas. 
                      Configure each node with the appropriate settings.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    3
                  </div>
                  <div>
                    <h4 className="font-medium">Connect the Flow</h4>
                    <p className="text-sm text-gray-600">
                      Draw connections between nodes to define the execution flow. 
                      Use conditions to create branching logic.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    4
                  </div>
                  <div>
                    <h4 className="font-medium">Test and Deploy</h4>
                    <p className="text-sm text-gray-600">
                      Use the built-in testing tools to validate your workflow. 
                      Deploy when ready and monitor performance.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Best Practices</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h4 className="font-medium">Design Principles</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Keep workflows focused and modular</li>
                    <li>• Use clear naming conventions</li>
                    <li>• Add error handling at critical points</li>
                    <li>• Document complex logic</li>
                  </ul>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-medium">Performance Tips</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Minimize AI agent calls when possible</li>
                    <li>• Use caching for repeated operations</li>
                    <li>• Set appropriate timeouts</li>
                    <li>• Monitor execution metrics</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Examples Tab */}
        <TabsContent value="examples" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Content Generation Pipeline</CardTitle>
                <CardDescription>
                  Automated content creation with review and optimization
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm text-gray-600">
                  <p className="mb-2"><strong>Flow:</strong></p>
                  <ol className="space-y-1">
                    <li>1. AI Agent generates initial content</li>
                    <li>2. Tool checks for plagiarism</li>
                    <li>3. Condition evaluates quality score</li>
                    <li>4. AI Agent optimizes if needed</li>
                    <li>5. Tool publishes final content</li>
                  </ol>
                </div>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => router.push('/dashboard/hybrids/create?template=content-generation')}
                >
                  Use Template
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Customer Support Bot</CardTitle>
                <CardDescription>
                  Intelligent support with human escalation
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm text-gray-600">
                  <p className="mb-2"><strong>Flow:</strong></p>
                  <ol className="space-y-1">
                    <li>1. AI Agent analyzes customer query</li>
                    <li>2. Tool searches knowledge base</li>
                    <li>3. Condition checks confidence level</li>
                    <li>4. AI Agent provides response or escalates</li>
                    <li>5. Tool logs interaction</li>
                  </ol>
                </div>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => router.push('/dashboard/hybrids/create?template=customer-support')}
                >
                  Use Template
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* API Reference Tab */}
        <TabsContent value="api" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Code className="h-5 w-5 mr-2" />
                SDK Integration
              </CardTitle>
              <CardDescription>
                Integrate hybrid workflows into your applications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">JavaScript/TypeScript</h4>
                <div className="bg-gray-100 p-3 rounded text-sm font-mono">
                  <pre>{`import { SynapseAI } from '@synapseai/sdk';

const client = new SynapseAI({
  apiKey: 'your-api-key',
  organizationId: 'your-org-id'
});

// Execute a workflow
const result = await client.hybrids.execute({
  workflowId: 'workflow-123',
  input: { message: 'Hello world' }
});`}</pre>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">REST API</h4>
                <div className="bg-gray-100 p-3 rounded text-sm font-mono">
                  <pre>{`POST /api/v1/hybrids/execute
Content-Type: application/json
Authorization: Bearer your-api-key

{
  "workflowId": "workflow-123",
  "input": {
    "message": "Hello world"
  }
}`}</pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
