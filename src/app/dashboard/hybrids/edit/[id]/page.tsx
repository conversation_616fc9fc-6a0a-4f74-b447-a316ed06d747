"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

// This is a redirect page that takes the ID from the URL and redirects to the create page with the ID as a query parameter
export default function EditHybridWorkflowPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { id } = params;

  useEffect(() => {
    if (id) {
      router.push(`/dashboard/hybrids/create?id=${id}&mode=edit`);
    } else {
      router.push('/dashboard/hybrids');
    }
  }, [id, router]);

  return (
    <div className="flex items-center justify-center h-screen">
      <div className="text-center">
        <h2 className="text-xl font-semibold mb-2">Redirecting...</h2>
        <p className="text-gray-500">Loading workflow editor</p>
      </div>
    </div>
  );
}
