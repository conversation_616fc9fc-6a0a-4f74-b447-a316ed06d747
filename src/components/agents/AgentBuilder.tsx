"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "../ui/card";
import { <PERSON><PERSON> } from "../ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "../ui/tabs";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Switch } from "../ui/switch";
import { Separator } from "../ui/separator";
import { Badge } from "../ui/badge";
import {
  AlertCircle,
  Check,
  ChevronRight,
  Code,
  Cog,
  Copy,
  Play,
  Save,
  Settings,
  Trash,
  Shield,
  Activity,
  Database,
  Wifi,
  WifiOff,
  Sparkles,
  Zap,
  Brain,
  Target,
  Clock,
  DollarSign,
  TrendingUp,
  Users,
  Globe,
  Lock,
  Eye,
  EyeOff,
  RefreshCw,
  Download,
  Upload,
  FileText,
  Layers,
  BarChart3,
} from "lucide-react";
import { Alert, AlertDescription } from "../ui/alert";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import PromptTemplateSelector from "./PromptTemplateSelector";
import { getAPXClient, type APXEvent } from "../../lib/apix-client";
import { mockAuthContext, hasPermission } from "../../lib/auth-context";
import { getSessionManager } from "../../lib/session-manager";
import { PromptTemplate } from "@/lib/prompt-template";
import { ScrollArea } from "../ui/scroll-area";

interface AgentBuilderProps {
  agentId?: string;
  initialData?: AgentData;
  onSave?: (data: AgentData) => void;
  onTest?: (data: AgentData) => void;
}

interface AgentData {
  name: string;
  description: string;
  provider: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  templateId?: string;
  isPublic: boolean;
  version: string;
}

const AgentBuilder: React.FC<AgentBuilderProps> = ({
  agentId,
  initialData,
  onSave = () => {},
  onTest = () => {},
}) => {
  const defaultData: AgentData = {
    name: "",
    description: "",
    provider: "openai",
    model: "gpt-4o",
    temperature: 0.7,
    maxTokens: 2048,
    systemPrompt: "You are a helpful AI assistant.",
    templateId: undefined,
    isPublic: false,
    version: "1.0.0",
  };

  const [agentData, setAgentData] = useState<AgentData>(
    initialData || defaultData,
  );
  const [activeTab, setActiveTab] = useState("settings");
  const [isTesting, setIsTesting] = useState(false);
  const [testInput, setTestInput] = useState("");
  const [testResponse, setTestResponse] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | undefined>(
    agentData.templateId,
  );
  const [notification, setNotification] = useState<{
    type: "success" | "error" | "warning" | "info";
    message: string;
  } | null>(null);
  const [testMetrics, setTestMetrics] = useState<{
    latency: number;
    cost: number;
    tokens: { promptTokens: number; completionTokens: number; totalTokens: number };
    provider: string;
    model: string;
    executionId: string;
  } | null>(null);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [agentPreview, setAgentPreview] = useState<string>("");

  // APIX Real-Time Engine state
  const [isConnected, setIsConnected] = useState(false);
  const [realtimeEvents, setRealtimeEvents] = useState<APXEvent[]>([]);
  const [sessionId, setSessionId] = useState<string | null>(null);

  // Auth & RBAC state
  const {
    user,
    organization,
    hasPermission: checkPermission,
  } = mockAuthContext;
  const canCreateAgent = checkPermission("agents", "create");
  const canEditAgent = checkPermission("agents", "update");
  const canDeleteAgent = checkPermission("agents", "delete");
  const canTestAgent = checkPermission("agents", "execute");

  // Session Manager
  const sessionManager = getSessionManager(organization?.id || "default");

  // Quota enforcement
  const quotaUsage = organization?.usage || {
    agents: 0,
    executions: 0,
    storage: 0,
    apiCalls: 0,
  };
  const quotaLimits = organization?.quotas || {
    agents: 100,
    executions: 10000,
    storage: 1000000000,
    apiCalls: 50000,
  };
  const isQuotaExceeded = quotaUsage.agents >= quotaLimits.agents;

  // Initialize APIX connection and session
  useEffect(() => {
    const initializeAPX = async () => {
      try {
        if (user && organization) {
          const apixClient = getAPXClient(
            organization.id,
            user.id,
            mockAuthContext.token!,
          );
          await apixClient.connect();
          setIsConnected(true);

          // Subscribe to agent events
          apixClient.subscribe(
            "agents",
            ["created", "updated", "deleted", "tested"],
            handleRealtimeEvent,
          );

          // Create session for this agent builder instance
          const newSessionId = await sessionManager.createSession(
            "agents",
            "user",
            { agentId, builderState: agentData },
            {
              tags: ["agent-builder"],
              conversationId: `builder-${Date.now()}`,
            },
          );
          setSessionId(newSessionId);
        }
      } catch (error) {
        console.error("Failed to initialize APIX:", error);
        setIsConnected(false);
      }
    };

    initializeAPX();

    return () => {
      // Cleanup on unmount
      if (sessionId) {
        sessionManager.deleteSession(sessionId);
      }
    };
  }, [user, organization]);

  const handleRealtimeEvent = useCallback(
    (event: APXEvent) => {
      setRealtimeEvents((prev) => [event, ...prev.slice(0, 9)]); // Keep last 10 events

      // Handle specific event types
      switch (event.type) {
        case "updated":
          if (event.data.agentId === agentId) {
            // Agent was updated by another user - show notification
            console.log("Agent updated by another user:", event.data);
          }
          break;
        case "tested":
          if (event.data.agentId === agentId) {
            // Real-time test results
            setTestResponse(event.data.response);
            setIsTesting(false);
          }
          break;
      }
    },
    [agentId],
  );

  const handleInputChange = (field: keyof AgentData, value: any) => {
    setAgentData((prev) => {
      const newData = {
        ...prev,
        [field]: value,
      };

      // Update session with new state
      if (sessionId) {
        sessionManager.updateSession(sessionId, { builderState: newData });
      }

      // Broadcast real-time update
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `agent-update-${Date.now()}`,
          type: "agent_builder_change",
          module: "agents",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: { agentId, field, value, builderState: newData },
          version: 1,
        });
      }

      return newData;
    });
  };

  const handleSave = async () => {
    // Check permissions
    if (!canCreateAgent && !canEditAgent) {
      setNotification({
        type: "error",
        message: "You do not have permission to save agents.",
      });
      return;
    }

    // Check quota limits
    if (!agentId && isQuotaExceeded) {
      setNotification({
        type: "error",
        message: `Agent quota exceeded. You have used ${quotaUsage.agents}/${quotaLimits.agents} agents.`,
      });
      return;
    }

    setIsSaving(true);
    try {
      // Validate required fields
      if (!agentData.name.trim()) {
        throw new Error("Agent name is required");
      }

      if (!agentData.systemPrompt.trim()) {
        throw new Error("System prompt is required");
      }

      // Prepare API request data
      const requestData = {
        name: agentData.name,
        description: agentData.description,
        provider: agentData.provider,
        model: agentData.model,
        temperature: agentData.temperature,
        maxTokens: agentData.maxTokens,
        systemPrompt: agentData.systemPrompt,
        templateId: agentData.templateId,
        isPublic: agentData.isPublic,
        version: agentData.version,
        metadata: {
          createdVia: "agent-builder",
          sessionId,
          builderVersion: "2.0.0",
          capabilities: getAgentCapabilities(),
        },
      };

      // Make API call to save agent
      const apiUrl = agentId ? `/api/agents/${agentId}` : "/api/agents";
      const method = agentId ? "PUT" : "POST";

      const response = await fetch(apiUrl, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${mockAuthContext.token}`,
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save agent");
      }

      const result = await response.json();
      const savedAgent = result.data.agent;
      const apiSessionId = result.data.sessionId;

      // Update local state with saved agent data
      setAgentData({
        ...agentData,
        ...savedAgent,
      });

      // Update session with save confirmation
      if (sessionId) {
        await sessionManager.updateSession(sessionId, {
          builderState: savedAgent,
          lastSaved: new Date().toISOString(),
          apiSessionId,
          savedAgentId: savedAgent.id,
        });
      }

      // Call parent callback
      onSave(savedAgent);

      // Show success notification
      setNotification({
        type: "success",
        message: `Agent ${agentId ? "updated" : "created"} successfully!`,
      });

      // Auto-hide notification after 3 seconds
      setTimeout(() => setNotification(null), 3000);
    } catch (error) {
      console.error("Error saving agent:", error);
      setNotification({
        type: "error",
        message: `Error saving agent: ${error instanceof Error ? error.message : "Unknown error"}`,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleTest = async () => {
    if (!testInput.trim()) {
      setNotification({
        type: "error",
        message: "Please enter a test prompt",
      });
      return;
    }

    // Check permissions
    if (!canTestAgent) {
      setNotification({
        type: "error",
        message: "You do not have permission to test agents.",
      });
      return;
    }

    // Check execution quota
    if (quotaUsage.executions >= quotaLimits.executions) {
      setNotification({
        type: "error",
        message: `Execution quota exceeded. You have used ${quotaUsage.executions}/${quotaLimits.executions} executions.`,
      });
      return;
    }

    setIsTesting(true);
    setTestResponse("");
    setTestMetrics(null);

    try {
      // Create test session
      const testSessionId = await sessionManager.createSession(
        "agents",
        "agent",
        {
          agentConfig: agentData,
          testInput,
          startTime: new Date().toISOString(),
        },
        {
          parentSessionId: sessionId || "default",
          tags: ["test", "agent-execution"],
          conversationId: `test-${Date.now()}`,
        },
      );

      // Broadcast test start event
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `agent-test-start-${Date.now()}`,
          type: "test_started",
          module: "agents",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            agentId: agentId || "new-agent",
            testSessionId,
            input: testInput,
            config: agentData,
          },
          version: 1,
        });
      }

      // Make real API call to test endpoint
      const testStartTime = Date.now();
      const response = await fetch("/api/agents/test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${mockAuthContext.token}`,
        },
        body: JSON.stringify({
          agentId: agentId,
          agentConfig: !agentId ? agentData : undefined,
          input: testInput,
          metadata: {
            testSessionId,
            builderVersion: "2.0.0",
          },
        }),
      });

      const testEndTime = Date.now();
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Test failed");
      }

      if (result.success) {
        setTestResponse(result.data.output);
        setTestMetrics({
          latency: result.data.latency,
          cost: result.data.cost,
          tokens: result.data.usage,
          provider: result.data.provider,
          model: result.data.model,
          executionId: result.data.executionId,
        });

        // Update test session with results
        await sessionManager.updateSession(testSessionId, {
          response: result.data.output,
          cost: result.data.cost,
          latency: result.data.latency,
          tokens: result.data.usage,
          endTime: new Date().toISOString(),
          success: true,
        });

        // Broadcast test completion
        if (isConnected && user && organization) {
          const apixClient = getAPXClient(
            organization.id,
            user.id,
            mockAuthContext.token!,
          );
          apixClient.sendEvent({
            id: `agent-test-complete-${Date.now()}`,
            type: "tested",
            module: "agents",
            organizationId: organization.id,
            userId: user.id,
            timestamp: new Date().toISOString(),
            data: {
              agentId: agentId || "new-agent",
              testSessionId,
              response: result.data.output,
              metrics: result.data,
              success: true,
            },
            version: 1,
          });
        }

        setNotification({
          type: "success",
          message: "Agent tested successfully!",
        });
      } else {
        throw new Error(result.error || "Test failed");
      }
    } catch (error) {
      console.error("Error testing agent:", error);
      const errorMessage = error instanceof Error ? error.message : "An error occurred while testing the agent.";
      setTestResponse(`Error: ${errorMessage}`);

      setNotification({
        type: "error",
        message: errorMessage,
      });

      // Broadcast test error
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `agent-test-error-${Date.now()}`,
          type: "test_error",
          module: "agents",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            agentId: agentId || "new-agent",
            error: errorMessage,
          },
          version: 1,
        });
      }
    } finally {
      setIsTesting(false);
    }
  };

  const handleTemplateSelect = (template: any) => {
    setSelectedTemplate(template.id);
    handleInputChange("templateId", template.id);
    handleInputChange(
      "systemPrompt",
      template.content || template.systemPrompt || template.description,
    );
    
    // Update agent name if not set
    if (!agentData.name.trim()) {
      handleInputChange("name", `${template.name} Agent`);
    }
    
    // Update description if not set
    if (!agentData.description.trim()) {
      handleInputChange("description", `Agent based on ${template.name} template: ${template.description}`);
    }

    setNotification({
      type: "success",
      message: `Template "${template.name}" applied successfully!`,
    });

    setTimeout(() => setNotification(null), 3000);
  };

  const getAgentCapabilities = () => {
    const capabilities = [];
    if (agentData.temperature > 0.7) capabilities.push("creative");
    if (agentData.temperature < 0.3) capabilities.push("deterministic");
    if (agentData.maxTokens > 2048) capabilities.push("long-form");
    if (agentData.provider === "openai" && agentData.model.includes("gpt-4")) capabilities.push("advanced-reasoning");
    if (agentData.systemPrompt.toLowerCase().includes("code")) capabilities.push("code-generation");
    if (agentData.systemPrompt.toLowerCase().includes("analysis")) capabilities.push("data-analysis");
    return capabilities;
  };

  const generateAgentPreview = () => {
    const capabilities = getAgentCapabilities();
    const preview = `🤖 ${agentData.name || "Untitled Agent"}

📝 Description: ${agentData.description || "No description provided"}

⚙️ Configuration:
• Provider: ${agentData.provider} (${agentData.model})
• Temperature: ${agentData.temperature} (${agentData.temperature > 0.7 ? "Creative" : agentData.temperature < 0.3 ? "Deterministic" : "Balanced"})
• Max Tokens: ${agentData.maxTokens}
• Version: ${agentData.version}

🎯 Capabilities: ${capabilities.length > 0 ? capabilities.join(", ") : "General purpose"}

🔒 Visibility: ${agentData.isPublic ? "Public" : "Private"}

💡 System Prompt Preview:
${agentData.systemPrompt.substring(0, 200)}${agentData.systemPrompt.length > 200 ? "..." : ""}`;
    
    setAgentPreview(preview);
  };

  // Update preview when agent data changes
  useEffect(() => {
    generateAgentPreview();
  }, [agentData]);

  return (
    <div className="bg-background w-full h-full flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
        <div className="flex-1">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Brain className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  {agentId ? "Edit Agent" : "Create New Agent"}
                </h1>
                <p className="text-sm text-muted-foreground">
                  {agentId
                    ? `Editing agent ${agentId}`
                    : "Build your intelligent AI assistant"}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {/* Real-time connection status */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                      isConnected 
                        ? "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400" 
                        : "bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400"
                    }`}>
                      {isConnected ? (
                        <Wifi className="h-3 w-3" />
                      ) : (
                        <WifiOff className="h-3 w-3" />
                      )}
                      {isConnected ? "Live" : "Offline"}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      {isConnected
                        ? "Connected to APIX Real-time Engine"
                        : "Disconnected from APIX"}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* User role badge */}
              <Badge variant="outline" className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                {user?.role}
              </Badge>

              {/* Organization badge */}
              <Badge variant="secondary" className="flex items-center gap-1">
                <Globe className="h-3 w-3" />
                {organization?.name}
              </Badge>
            </div>
          </div>
          
          {/* Quota usage indicators */}
          <div className="flex items-center gap-6 mt-3">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-muted-foreground" />
              <div className="flex items-center gap-2">
                <div className="w-20 bg-muted rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all ${
                      quotaUsage.agents >= quotaLimits.agents * 0.9
                        ? "bg-red-500"
                        : quotaUsage.agents >= quotaLimits.agents * 0.7
                        ? "bg-yellow-500"
                        : "bg-green-500"
                    }`}
                    style={{ width: `${Math.min((quotaUsage.agents / quotaLimits.agents) * 100, 100)}%` }}
                  />
                </div>
                <span className="text-xs text-muted-foreground">
                  {quotaUsage.agents}/{quotaLimits.agents} agents
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-muted-foreground" />
              <div className="flex items-center gap-2">
                <div className="w-20 bg-muted rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all ${
                      quotaUsage.executions >= quotaLimits.executions * 0.9
                        ? "bg-red-500"
                        : quotaUsage.executions >= quotaLimits.executions * 0.7
                        ? "bg-yellow-500"
                        : "bg-blue-500"
                    }`}
                    style={{ width: `${Math.min((quotaUsage.executions / quotaLimits.executions) * 100, 100)}%` }}
                  />
                </div>
                <span className="text-xs text-muted-foreground">
                  {quotaUsage.executions}/{quotaLimits.executions} executions
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
                >
                  <Settings className="mr-2 h-4 w-4" />
                  {showAdvancedSettings ? "Hide" : "Show"} Advanced
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Toggle advanced configuration options</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => {}}
            disabled={!canCreateAgent}
          >
            <Copy className="mr-2 h-4 w-4" /> Duplicate
          </Button>
          
          <Button
            variant="destructive"
            size="sm"
            onClick={() => {}}
            disabled={!canDeleteAgent}
          >
            <Trash className="mr-2 h-4 w-4" /> Delete
          </Button>
          
          <Button
            onClick={handleSave}
            size="sm"
            disabled={
              isSaving ||
              (!canCreateAgent && !canEditAgent) ||
              (!agentId && isQuotaExceeded)
            }
            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
          >
            {isSaving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" /> Save Agent
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Notification */}
      {notification && (
        <div className={`px-6 py-3 border-b ${
          notification.type === "success" 
            ? "bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800" 
            : notification.type === "error"
            ? "bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800"
            : notification.type === "warning"
            ? "bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800"
            : "bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800"
        }`}>
          <div className="flex items-center gap-2">
            {notification.type === "success" && <Check className="h-4 w-4 text-green-600" />}
            {notification.type === "error" && <AlertCircle className="h-4 w-4 text-red-600" />}
            {notification.type === "warning" && <AlertCircle className="h-4 w-4 text-yellow-600" />}
            {notification.type === "info" && <AlertCircle className="h-4 w-4 text-blue-600" />}
            <span className={`text-sm font-medium ${
              notification.type === "success" 
                ? "text-green-800 dark:text-green-200" 
                : notification.type === "error"
                ? "text-red-800 dark:text-red-200"
                : notification.type === "warning"
                ? "text-yellow-800 dark:text-yellow-200"
                : "text-blue-800 dark:text-blue-200"
            }`}>
              {notification.message}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setNotification(null)}
              className="ml-auto h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        </div>
      )}

      {/* Real-time events panel */}
      {realtimeEvents.length > 0 && (
        <div className="px-6 py-3 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 border-b">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <Activity className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-900 dark:text-blue-100">Live Events:</span>
            </div>
            <div className="flex gap-2 overflow-x-auto">
              {realtimeEvents.slice(0, 3).map((event, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="text-xs whitespace-nowrap bg-white/50 border-blue-200"
                >
                  <Zap className="h-3 w-3 mr-1" />
                  {event.type} •{" "}
                  {new Date(event.timestamp).toLocaleTimeString()}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Quota warning */}
      {isQuotaExceeded && (
        <Alert className="mx-6 mt-4 border-red-200 bg-red-50 dark:bg-red-900/20">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800 dark:text-red-200">
            <strong>Agent quota exceeded.</strong> You have reached the limit of{" "}
            {quotaLimits.agents} agents for your organization. Please contact
            your administrator to increase your quota or upgrade your plan.
          </AlertDescription>
        </Alert>
      )}

      <div className="flex flex-1 overflow-hidden">
        <div className="flex-1 flex overflow-hidden">
          {/* Main Configuration Panel */}
          <div className="flex-1 p-6 overflow-y-auto">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="mb-6 bg-muted/50">
                <TabsTrigger value="settings" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" /> 
                  Basic Settings
                </TabsTrigger>
                <TabsTrigger value="prompt" className="flex items-center gap-2">
                  <Code className="h-4 w-4" /> 
                  Prompt & Templates
                </TabsTrigger>
                <TabsTrigger value="advanced" className="flex items-center gap-2">
                  <Cog className="h-4 w-4" /> 
                  Advanced Config
                </TabsTrigger>
                <TabsTrigger value="preview" className="flex items-center gap-2">
                  <Eye className="h-4 w-4" /> 
                  Preview
                </TabsTrigger>
              </TabsList>

            <TabsContent value="settings" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Basic Information */}
                <Card className="border-2 border-dashed border-muted-foreground/20 hover:border-primary/50 transition-colors">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-2">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                        <FileText className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">Basic Information</CardTitle>
                        <CardDescription>
                          Configure the essential details for your agent
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        Agent Name *
                      </Label>
                      <Input
                        id="name"
                        value={agentData.name}
                        onChange={(e) =>
                          handleInputChange("name", e.target.value)
                        }
                        placeholder="e.g., Customer Support Assistant"
                        className="font-medium"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="description" className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Description
                      </Label>
                      <Textarea
                        id="description"
                        value={agentData.description}
                        onChange={(e) =>
                          handleInputChange("description", e.target.value)
                        }
                        placeholder="Describe what this agent does and its primary purpose..."
                        rows={3}
                        className="resize-none"
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="version" className="flex items-center gap-2">
                          <Layers className="h-4 w-4" />
                          Version
                        </Label>
                        <Input
                          id="version"
                          value={agentData.version}
                          onChange={(e) =>
                            handleInputChange("version", e.target.value)
                          }
                          placeholder="1.0.0"
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label className="flex items-center gap-2">
                          {agentData.isPublic ? (
                            <Globe className="h-4 w-4 text-green-600" />
                          ) : (
                            <Lock className="h-4 w-4 text-gray-600" />
                          )}
                          Visibility
                        </Label>
                        <div className="flex items-center space-x-3 p-3 border rounded-lg">
                          <Switch
                            id="isPublic"
                            checked={agentData.isPublic}
                            onCheckedChange={(checked) =>
                              handleInputChange("isPublic", checked)
                            }
                          />
                          <Label htmlFor="isPublic" className="text-sm">
                            {agentData.isPublic ? "Public" : "Private"}
                          </Label>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Provider Configuration */}
                <Card className="border-2 border-dashed border-muted-foreground/20 hover:border-primary/50 transition-colors">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-2">
                      <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                        <Sparkles className="h-4 w-4 text-purple-600" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">AI Provider</CardTitle>
                        <CardDescription>
                          Choose the AI model and provider for your agent
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="provider" className="flex items-center gap-2">
                        <Brain className="h-4 w-4" />
                        Provider
                      </Label>
                      <Select
                        value={agentData.provider}
                        onValueChange={(value) => {
                          handleInputChange("provider", value);
                          // Reset model when provider changes
                          if (value === "openai") handleInputChange("model", "gpt-4o");
                          else if (value === "anthropic") handleInputChange("model", "claude-3-sonnet");
                          else if (value === "google") handleInputChange("model", "gemini-pro");
                          else if (value === "mistral") handleInputChange("model", "mistral-large");
                          else if (value === "groq") handleInputChange("model", "llama3-70b");
                        }}
                      >
                        <SelectTrigger id="provider" className="h-12">
                          <SelectValue placeholder="Select AI provider" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="openai" className="p-3">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                <span className="text-green-600 font-bold text-xs">AI</span>
                              </div>
                              <div>
                                <div className="font-medium">OpenAI</div>
                                <div className="text-xs text-muted-foreground">GPT-4, GPT-3.5</div>
                              </div>
                            </div>
                          </SelectItem>
                          <SelectItem value="anthropic" className="p-3">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                <span className="text-orange-600 font-bold text-xs">C</span>
                              </div>
                              <div>
                                <div className="font-medium">Anthropic</div>
                                <div className="text-xs text-muted-foreground">Claude 3 Series</div>
                              </div>
                            </div>
                          </SelectItem>
                          <SelectItem value="google" className="p-3">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <span className="text-blue-600 font-bold text-xs">G</span>
                              </div>
                              <div>
                                <div className="font-medium">Google AI</div>
                                <div className="text-xs text-muted-foreground">Gemini Pro, Ultra</div>
                              </div>
                            </div>
                          </SelectItem>
                          <SelectItem value="mistral" className="p-3">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                                <span className="text-red-600 font-bold text-xs">M</span>
                              </div>
                              <div>
                                <div className="font-medium">Mistral AI</div>
                                <div className="text-xs text-muted-foreground">Large, Medium, Small</div>
                              </div>
                            </div>
                          </SelectItem>
                          <SelectItem value="groq" className="p-3">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <span className="text-yellow-600 font-bold text-xs">⚡</span>
                              </div>
                              <div>
                                <div className="font-medium">Groq</div>
                                <div className="text-xs text-muted-foreground">Ultra-fast inference</div>
                              </div>
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="model" className="flex items-center gap-2">
                        <Zap className="h-4 w-4" />
                        Model
                      </Label>
                      <Select
                        value={agentData.model}
                        onValueChange={(value) =>
                          handleInputChange("model", value)
                        }
                      >
                        <SelectTrigger id="model" className="h-12">
                          <SelectValue placeholder="Select model" />
                        </SelectTrigger>
                        <SelectContent>
                          {agentData.provider === "openai" && (
                            <>
                              <SelectItem value="gpt-4o" className="p-3">
                                <div>
                                  <div className="font-medium">GPT-4o</div>
                                  <div className="text-xs text-muted-foreground">Latest, most capable</div>
                                </div>
                              </SelectItem>
                              <SelectItem value="gpt-4-turbo" className="p-3">
                                <div>
                                  <div className="font-medium">GPT-4 Turbo</div>
                                  <div className="text-xs text-muted-foreground">Fast and powerful</div>
                                </div>
                              </SelectItem>
                              <SelectItem value="gpt-3.5-turbo" className="p-3">
                                <div>
                                  <div className="font-medium">GPT-3.5 Turbo</div>
                                  <div className="text-xs text-muted-foreground">Cost-effective</div>
                                </div>
                              </SelectItem>
                            </>
                          )}
                          {agentData.provider === "anthropic" && (
                            <>
                              <SelectItem value="claude-3-opus" className="p-3">
                                <div>
                                  <div className="font-medium">Claude 3 Opus</div>
                                  <div className="text-xs text-muted-foreground">Most intelligent</div>
                                </div>
                              </SelectItem>
                              <SelectItem value="claude-3-sonnet" className="p-3">
                                <div>
                                  <div className="font-medium">Claude 3 Sonnet</div>
                                  <div className="text-xs text-muted-foreground">Balanced performance</div>
                                </div>
                              </SelectItem>
                              <SelectItem value="claude-3-haiku" className="p-3">
                                <div>
                                  <div className="font-medium">Claude 3 Haiku</div>
                                  <div className="text-xs text-muted-foreground">Fast and efficient</div>
                                </div>
                              </SelectItem>
                            </>
                          )}
                          {agentData.provider === "google" && (
                            <>
                              <SelectItem value="gemini-pro" className="p-3">
                                <div>
                                  <div className="font-medium">Gemini Pro</div>
                                  <div className="text-xs text-muted-foreground">Versatile model</div>
                                </div>
                              </SelectItem>
                              <SelectItem value="gemini-ultra" className="p-3">
                                <div>
                                  <div className="font-medium">Gemini Ultra</div>
                                  <div className="text-xs text-muted-foreground">Most capable</div>
                                </div>
                              </SelectItem>
                            </>
                          )}
                          {agentData.provider === "mistral" && (
                            <>
                              <SelectItem value="mistral-large" className="p-3">
                                <div>
                                  <div className="font-medium">Mistral Large</div>
                                  <div className="text-xs text-muted-foreground">Most powerful</div>
                                </div>
                              </SelectItem>
                              <SelectItem value="mistral-medium" className="p-3">
                                <div>
                                  <div className="font-medium">Mistral Medium</div>
                                  <div className="text-xs text-muted-foreground">Balanced option</div>
                                </div>
                              </SelectItem>
                              <SelectItem value="mistral-small" className="p-3">
                                <div>
                                  <div className="font-medium">Mistral Small</div>
                                  <div className="text-xs text-muted-foreground">Fast and efficient</div>
                                </div>
                              </SelectItem>
                            </>
                          )}
                          {agentData.provider === "groq" && (
                            <>
                              <SelectItem value="llama3-70b" className="p-3">
                                <div>
                                  <div className="font-medium">LLaMA 3 70B</div>
                                  <div className="text-xs text-muted-foreground">Large, capable model</div>
                                </div>
                              </SelectItem>
                              <SelectItem value="llama3-8b" className="p-3">
                                <div>
                                  <div className="font-medium">LLaMA 3 8B</div>
                                  <div className="text-xs text-muted-foreground">Compact and fast</div>
                                </div>
                              </SelectItem>
                              <SelectItem value="mixtral-8x7b" className="p-3">
                                <div>
                                  <div className="font-medium">Mixtral 8x7B</div>
                                  <div className="text-xs text-muted-foreground">Mixture of experts</div>
                                </div>
                              </SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="prompt" className="space-y-6">
              <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
                {/* Template Selector */}
                <div className="xl:col-span-1">
                  <Card className="h-fit">
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-2">
                        <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                          <Sparkles className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">Templates</CardTitle>
                          <CardDescription>
                            Choose from pre-built templates
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant={selectedTemplate ? "default" : "outline"} className="text-xs">
                          {selectedTemplate ? "Template Active" : "No Template"}
                        </Badge>
                        {selectedTemplate && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedTemplate(undefined);
                              handleInputChange("templateId", undefined);
                              setNotification({
                                type: "info",
                                message: "Template cleared",
                              });
                            }}
                            className="h-6 px-2 text-xs"
                          >
                            Clear
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="p-0">
                      <div className="max-h-96 overflow-hidden">
                        <PromptTemplateSelector
                          selectedTemplateId={selectedTemplate}
                          onSelectTemplate={handleTemplateSelect}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* System Prompt Editor */}
                <div className="xl:col-span-2">
                  <Card className="h-full">
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                            <Code className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <CardTitle className="text-lg">System Prompt</CardTitle>
                            <CardDescription>
                              Define your agent's behavior and personality
                            </CardDescription>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {agentData.systemPrompt.length} characters
                          </Badge>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <AlertCircle className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent className="max-w-xs">
                                <p className="text-sm">
                                  The system prompt is the core instruction that defines how your agent behaves. 
                                  Be specific about the agent's role, capabilities, and response style.
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="systemPrompt" className="flex items-center gap-2">
                            <Brain className="h-4 w-4" />
                            Prompt Content *
                          </Label>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const examples = [
                                  "You are a helpful AI assistant that provides clear, accurate, and concise responses.",
                                  "You are a customer service representative for a tech company. Be friendly, professional, and solution-oriented.",
                                  "You are a creative writing assistant. Help users brainstorm ideas, improve their writing, and overcome writer's block.",
                                  "You are a data analyst. Provide insights, explain trends, and help users understand their data."
                                ];
                                const randomExample = examples[Math.floor(Math.random() * examples.length)];
                                handleInputChange("systemPrompt", randomExample);
                                setNotification({
                                  type: "info",
                                  message: "Example prompt loaded",
                                });
                              }}
                              className="text-xs h-7"
                            >
                              <Sparkles className="h-3 w-3 mr-1" />
                              Example
                            </Button>
                          </div>
                        </div>
                        <Textarea
                          id="systemPrompt"
                          value={agentData.systemPrompt}
                          onChange={(e) =>
                            handleInputChange("systemPrompt", e.target.value)
                          }
                          placeholder="Enter your system prompt here...\n\nExample:\nYou are a helpful AI assistant specialized in customer support. You should:\n- Be friendly and professional\n- Provide clear and actionable solutions\n- Ask clarifying questions when needed\n- Escalate complex issues appropriately"
                          rows={12}
                          className="font-mono text-sm resize-none"
                        />
                      </div>
                      
                      <div className="bg-muted/50 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                          <div className="p-1 bg-blue-100 dark:bg-blue-900/20 rounded">
                            <AlertCircle className="h-4 w-4 text-blue-600" />
                          </div>
                          <div className="space-y-2 text-sm">
                            <p className="font-medium text-blue-900 dark:text-blue-100">
                              💡 Prompt Writing Tips:
                            </p>
                            <ul className="space-y-1 text-muted-foreground">
                              <li>• Be specific about the agent's role and expertise</li>
                              <li>• Define the tone and communication style</li>
                              <li>• Include examples of desired behavior</li>
                              <li>• Set clear boundaries and limitations</li>
                              <li>• Use templates for consistency across similar agents</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Advanced Settings</CardTitle>
                  <CardDescription>
                    Configure advanced parameters for your agent.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <Label htmlFor="temperature">
                          Temperature: {agentData.temperature}
                        </Label>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <AlertCircle className="h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                Controls randomness: 0 is deterministic, 1 is
                                creative
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <Input
                        id="temperature"
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        value={agentData.temperature}
                        onChange={(e) =>
                          handleInputChange(
                            "temperature",
                            parseFloat(e.target.value),
                          )
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <Label htmlFor="maxTokens">
                          Max Tokens: {agentData.maxTokens}
                        </Label>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <AlertCircle className="h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Maximum number of tokens to generate</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <Input
                        id="maxTokens"
                        type="range"
                        min="256"
                        max="4096"
                        step="256"
                        value={agentData.maxTokens}
                        onChange={(e) =>
                          handleInputChange(
                            "maxTokens",
                            parseInt(e.target.value),
                          )
                        }
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="w-1/3 border-l p-4 overflow-y-auto">
          <Card className="h-full flex flex-col">
            <CardHeader>
              <CardTitle>Test Your Agent</CardTitle>
              <CardDescription>
                Try out your agent with a test prompt to see how it responds.
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1 overflow-y-auto">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="testInput">Test Prompt</Label>
                  <Textarea
                    id="testInput"
                    value={testInput}
                    onChange={(e) => setTestInput(e.target.value)}
                    placeholder="Enter a test prompt for your agent..."
                    rows={4}
                  />
                </div>

                {testResponse && (
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Label className="flex-1">Response</Label>
                      <Badge variant="outline" className="ml-2">
                        {agentData.provider} / {agentData.model}
                      </Badge>
                    </div>
                    <ScrollArea className="bg-muted p-4 rounded-md font-mono text-sm max-h-[400px]">
                      <pre className="whitespace-pre-wrap">{testResponse}</pre>
                    </ScrollArea>
                  </div>
                )}

                {!testResponse && !isTesting && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Enter a test prompt and click "Test Agent" to see a
                      response.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
            <CardFooter className="border-t pt-4">
              <div className="space-y-2">
                <Button
                  onClick={handleTest}
                  disabled={
                    isTesting ||
                    !testInput.trim() ||
                    !canTestAgent ||
                    quotaUsage.executions >= quotaLimits.executions
                  }
                  className="w-full"
                >
                  {isTesting ? (
                    <>
                      <span className="animate-spin mr-2">⏳</span> Testing...
                    </>
                  ) : (
                    <>
                      <Play className="mr-2 h-4 w-4" /> Test Agent
                    </>
                  )}
                </Button>

                {/* Session info */}
                {sessionId && (
                  <div className="text-xs text-muted-foreground text-center">
                    Session: {sessionId.split("_").pop()}
                  </div>
                )}

                {/* Permission/quota warnings */}
                {!canTestAgent && (
                  <div className="text-xs text-red-500 text-center">
                    No permission to test agents
                  </div>
                )}
                {quotaUsage.executions >= quotaLimits.executions && (
                  <div className="text-xs text-red-500 text-center">
                    Execution quota exceeded
                  </div>
                )}
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
    </div>
  );
}

export default AgentBuilder;
