"use client";

import React, { useState, use<PERSON>allback, useRef, useEffect } from "react";
import { ReactFlowProvider, useReactFlow } from "@/lib/mock-reactflow";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Save,
  Play,
  Pause,
  Square,
  RotateCcw,
  Redo2,
  Undo2,
  Settings,
  Eye,
  EyeOff,
  Maximize2,
  Minimize2,
  Download,
  Upload,
  Share2,
  Users,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader2,
  Bot,
  Wrench,
  Diamond,
  Circle,
  Square as SquareIcon,
  User,
  GitBranch,
  Zap,
  Database,
  Globe,
  Code,
  MessageSquare,
  FileText,
  Trash2,
  Copy,
  Edit3,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  Plus,
  Minus,
  Search,
  Filter,
  MoreHorizontal,
  Info,
  Bug,
  Activity,
  Terminal,
  Layers,
  Grid,
  Move,
  MousePointer,
  Hand,
  ZoomIn,
  ZoomOut,
  RotateCw,
  RefreshCw,
  Wifi,
  WifiOff,
} from "lucide-react";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";
import { mockAuthContext } from "@/lib/auth-context";
import { cn } from "@/lib/utils";

// Import node components
import AgentNode from "./nodes/AgentNode";
import ToolNode from "./nodes/ToolNode";
import ConditionNode from "./nodes/ConditionNode";
import StartNode from "./nodes/StartNode";
import EndNode from "./nodes/EndNode";
import HITLNode from "./nodes/HITLNode";
import CustomEdge from "./edges/CustomEdge";

// Flow state management using Zustand
import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";

// Types
interface FlowNode {
  id: string;
  type: "start" | "end" | "agent" | "tool" | "condition" | "hitl";
  position: { x: number; y: number };
  data: {
    name: string;
    description?: string;
    status?: "idle" | "running" | "completed" | "failed" | "paused";
    config?: Record<string, any>;
    executionTime?: number;
    error?: string;
    output?: any;
    logs?: string[];
  };
  selected?: boolean;
  dragging?: boolean;
}

interface FlowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  type?: string;
  data?: {
    label?: string;
    condition?: string;
    animated?: boolean;
    status?: "idle" | "active" | "completed" | "failed";
  };
  selected?: boolean;
}

interface FlowState {
  // Core state
  nodes: FlowNode[];
  edges: FlowEdge[];
  selectedNodeId: string | null;
  selectedEdgeId: string | null;

  // Execution state
  isExecuting: boolean;
  executionId: string | null;
  executionLogs: Array<{
    timestamp: string;
    level: "info" | "warn" | "error";
    message: string;
    nodeId?: string;
  }>;

  // UI state
  sidebarCollapsed: boolean;
  inspectorCollapsed: boolean;
  logsCollapsed: boolean;
  miniMapVisible: boolean;
  gridVisible: boolean;

  // History for undo/redo
  history: Array<{ nodes: FlowNode[]; edges: FlowEdge[] }>;
  historyIndex: number;

  // Collaboration
  collaborators: Array<{
    id: string;
    name: string;
    cursor?: { x: number; y: number };
  }>;

  // Workflow metadata
  workflowId: string | null;
  workflowName: string;
  workflowDescription: string;
  lastSaved: Date | null;
  isDirty: boolean;
  version: number;

  // Actions
  setNodes: (nodes: FlowNode[]) => void;
  setEdges: (edges: FlowEdge[]) => void;
  addNode: (node: FlowNode) => void;
  updateNode: (id: string, updates: Partial<FlowNode["data"]>) => void;
  deleteNode: (id: string) => void;
  addEdge: (edge: FlowEdge) => void;
  deleteEdge: (id: string) => void;
  selectNode: (id: string | null) => void;
  selectEdge: (id: string | null) => void;

  // Execution actions
  startExecution: (input?: any) => void;
  pauseExecution: () => void;
  stopExecution: () => void;
  updateNodeStatus: (
    nodeId: string,
    status: FlowNode["data"]["status"],
    data?: any,
  ) => void;
  addLog: (log: FlowState["executionLogs"][0]) => void;

  // UI actions
  toggleSidebar: () => void;
  toggleInspector: () => void;
  toggleLogs: () => void;
  toggleMiniMap: () => void;
  toggleGrid: () => void;

  // History actions
  saveToHistory: () => void;
  undo: () => void;
  redo: () => void;
  canUndo: () => boolean;
  canRedo: () => boolean;

  // Workflow actions
  setWorkflowMetadata: (
    metadata: Partial<Pick<FlowState, "workflowName" | "workflowDescription">>,
  ) => void;
  markDirty: () => void;
  markClean: () => void;
}

// Zustand store
const useFlowStore = create<FlowState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    nodes: [
      {
        id: "start-1",
        type: "start",
        position: { x: 100, y: 200 },
        data: { name: "Start", status: "idle" },
      },
      {
        id: "end-1",
        type: "end",
        position: { x: 800, y: 200 },
        data: { name: "End", status: "idle" },
      },
    ],
    edges: [],
    selectedNodeId: null,
    selectedEdgeId: null,

    isExecuting: false,
    executionId: null,
    executionLogs: [],

    sidebarCollapsed: false,
    inspectorCollapsed: false,
    logsCollapsed: true,
    miniMapVisible: true,
    gridVisible: true,

    history: [],
    historyIndex: -1,

    collaborators: [],

    workflowId: null,
    workflowName: "Untitled Workflow",
    workflowDescription: "",
    lastSaved: null,
    isDirty: false,
    version: 1,

    // Actions
    setNodes: (nodes) => set({ nodes, isDirty: true }),
    setEdges: (edges) => set({ edges, isDirty: true }),

    addNode: (node) => {
      const state = get();
      state.saveToHistory();
      set({
        nodes: [...state.nodes, node],
        isDirty: true,
      });
    },

    updateNode: (id, updates) => {
      const state = get();
      set({
        nodes: state.nodes.map((node) =>
          node.id === id
            ? { ...node, data: { ...node.data, ...updates } }
            : node,
        ),
        isDirty: true,
      });
    },

    deleteNode: (id) => {
      const state = get();
      if (id.startsWith("start-") || id.startsWith("end-")) return; // Prevent deletion of start/end nodes

      state.saveToHistory();
      set({
        nodes: state.nodes.filter((node) => node.id !== id),
        edges: state.edges.filter(
          (edge) => edge.source !== id && edge.target !== id,
        ),
        selectedNodeId:
          state.selectedNodeId === id ? null : state.selectedNodeId,
        isDirty: true,
      });
    },

    addEdge: (edge) => {
      const state = get();
      state.saveToHistory();
      set({
        edges: [...state.edges, edge],
        isDirty: true,
      });
    },

    deleteEdge: (id) => {
      const state = get();
      state.saveToHistory();
      set({
        edges: state.edges.filter((edge) => edge.id !== id),
        selectedEdgeId:
          state.selectedEdgeId === id ? null : state.selectedEdgeId,
        isDirty: true,
      });
    },

    selectNode: (id) => set({ selectedNodeId: id, selectedEdgeId: null }),
    selectEdge: (id) => set({ selectedEdgeId: id, selectedNodeId: null }),

    // Execution actions
    startExecution: (input) => {
      const executionId = `exec-${Date.now()}`;
      set({
        isExecuting: true,
        executionId,
        executionLogs: [
          {
            timestamp: new Date().toISOString(),
            level: "info",
            message: "Workflow execution started",
          },
        ],
      });
    },

    pauseExecution: () => set({ isExecuting: false }),

    stopExecution: () => {
      const state = get();
      set({
        isExecuting: false,
        executionId: null,
        nodes: state.nodes.map((node) => ({
          ...node,
          data: { ...node.data, status: "idle" },
        })),
      });
    },

    updateNodeStatus: (nodeId, status, data) => {
      const state = get();
      set({
        nodes: state.nodes.map((node) =>
          node.id === nodeId
            ? {
                ...node,
                data: {
                  ...node.data,
                  status,
                  ...data,
                },
              }
            : node,
        ),
      });
    },

    addLog: (log) => {
      const state = get();
      set({
        executionLogs: [...state.executionLogs, log],
      });
    },

    // UI actions
    toggleSidebar: () =>
      set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed })),
    toggleInspector: () =>
      set((state) => ({ inspectorCollapsed: !state.inspectorCollapsed })),
    toggleLogs: () => set((state) => ({ logsCollapsed: !state.logsCollapsed })),
    toggleMiniMap: () =>
      set((state) => ({ miniMapVisible: !state.miniMapVisible })),
    toggleGrid: () => set((state) => ({ gridVisible: !state.gridVisible })),

    // History actions
    saveToHistory: () => {
      const state = get();
      const newHistory = state.history.slice(0, state.historyIndex + 1);
      newHistory.push({ nodes: [...state.nodes], edges: [...state.edges] });

      // Limit history size
      if (newHistory.length > 50) {
        newHistory.shift();
      } else {
        set({ historyIndex: state.historyIndex + 1 });
      }

      set({ history: newHistory });
    },

    undo: () => {
      const state = get();
      if (state.historyIndex > 0) {
        const previousState = state.history[state.historyIndex - 1];
        set({
          nodes: previousState.nodes,
          edges: previousState.edges,
          historyIndex: state.historyIndex - 1,
          isDirty: true,
        });
      }
    },

    redo: () => {
      const state = get();
      if (state.historyIndex < state.history.length - 1) {
        const nextState = state.history[state.historyIndex + 1];
        set({
          nodes: nextState.nodes,
          edges: nextState.edges,
          historyIndex: state.historyIndex + 1,
          isDirty: true,
        });
      }
    },

    canUndo: () => get().historyIndex > 0,
    canRedo: () => get().historyIndex < get().history.length - 1,

    // Workflow actions
    setWorkflowMetadata: (metadata) => set({ ...metadata, isDirty: true }),
    markDirty: () => set({ isDirty: true }),
    markClean: () => set({ isDirty: false, lastSaved: new Date() }),
  })),
);

// Node types configuration
const nodeTypes = {
  start: StartNode,
  end: EndNode,
  agent: AgentNode,
  tool: ToolNode,
  condition: ConditionNode,
  hitl: HITLNode,
};

const edgeTypes = {
  custom: CustomEdge,
};

// Node palette configuration
const nodePalette = [
  {
    type: "agent",
    label: "AI Agent",
    icon: Bot,
    description: "Execute AI agent with custom prompts",
    color: "blue",
    category: "AI",
  },
  {
    type: "tool",
    label: "Tool",
    icon: Wrench,
    description: "Execute external tools and APIs",
    color: "green",
    category: "Tools",
  },
  {
    type: "condition",
    label: "Condition",
    icon: Diamond,
    description: "Conditional branching logic",
    color: "yellow",
    category: "Logic",
  },
  {
    type: "hitl",
    label: "Human Review",
    icon: User,
    description: "Human-in-the-loop approval step",
    color: "purple",
    category: "Human",
  },
];

interface FlowWorkflowBuilderProps {
  workflowId?: string;
  initialData?: any;
  mode?: "create" | "edit" | "view" | "debug";
  onSave?: (data: any) => Promise<void>;
  onExecute?: (data: any, input?: any) => Promise<void>;
  className?: string;
}

// Main Flow Builder Component
function FlowWorkflowBuilderInner({
  workflowId,
  initialData,
  mode = "create",
  onSave,
  onExecute,
  className,
}: FlowWorkflowBuilderProps) {
  const {
    nodes,
    edges,
    selectedNodeId,
    selectedEdgeId,
    isExecuting,
    executionLogs,
    sidebarCollapsed,
    inspectorCollapsed,
    logsCollapsed,
    miniMapVisible,
    gridVisible,
    workflowName,
    workflowDescription,
    isDirty,
    lastSaved,
    addNode,
    updateNode,
    deleteNode,
    addEdge,
    deleteEdge,
    selectNode,
    selectEdge,
    startExecution,
    pauseExecution,
    stopExecution,
    toggleSidebar,
    toggleInspector,
    toggleLogs,
    toggleMiniMap,
    toggleGrid,
    undo,
    redo,
    canUndo,
    canRedo,
    setWorkflowMetadata,
    markClean,
  } = useFlowStore();

  const [isConnected, setIsConnected] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [draggedNodeType, setDraggedNodeType] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("All");

  const canvasRef = useRef<HTMLDivElement>(null);
  const reactFlow = useReactFlow();

  // Initialize APIX connection
  useEffect(() => {
    const initConnection = async () => {
      try {
        const { user, organization } = mockAuthContext;
        const apixClient = getAPXClient(
          organization?.id || "org-123",
          user?.id || "user-456",
          "mock-token",
        );

        await apixClient.connect();
        setIsConnected(true);

        // Subscribe to workflow execution events
        apixClient.subscribe(
          "hybrids",
          ["workflow_step_completed", "workflow_failed"],
          (event) => {
            if (event.type === "workflow_step_completed") {
              useFlowStore
                .getState()
                .updateNodeStatus(event.data.nodeId, "completed", {
                  output: event.data.output,
                  executionTime: event.data.duration,
                });
              useFlowStore.getState().addLog({
                timestamp: event.timestamp,
                level: "info",
                message: `Node ${event.data.nodeId} completed`,
                nodeId: event.data.nodeId,
              });
            } else if (event.type === "workflow_failed") {
              useFlowStore
                .getState()
                .updateNodeStatus(event.data.nodeId, "failed", {
                  error: event.data.error,
                });
              useFlowStore.getState().addLog({
                timestamp: event.timestamp,
                level: "error",
                message: `Node ${event.data.nodeId} failed: ${event.data.error}`,
                nodeId: event.data.nodeId,
              });
            }
          },
        );
      } catch (error) {
        console.error("Failed to connect to APIX:", error);
        setIsConnected(false);
      }
    };

    initConnection();
  }, []);

  // Auto-save functionality
  useEffect(() => {
    if (!isDirty || !onSave) return;

    const autoSaveTimer = setTimeout(async () => {
      try {
        setIsSaving(true);
        await onSave({
          id: workflowId,
          name: workflowName,
          description: workflowDescription,
          nodes,
          edges,
          version: useFlowStore.getState().version + 1,
        });
        markClean();
      } catch (error) {
        console.error("Auto-save failed:", error);
      } finally {
        setIsSaving(false);
      }
    }, 2000);

    return () => clearTimeout(autoSaveTimer);
  }, [
    isDirty,
    nodes,
    edges,
    workflowName,
    workflowDescription,
    onSave,
    workflowId,
    markClean,
  ]);

  // Drag and drop handlers
  const onDragStart = useCallback(
    (event: React.DragEvent, nodeType: string) => {
      setDraggedNodeType(nodeType);
      event.dataTransfer.effectAllowed = "move";
    },
    [],
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      if (!draggedNodeType || !canvasRef.current) return;

      const canvasRect = canvasRef.current.getBoundingClientRect();
      const position = {
        x: event.clientX - canvasRect.left - 100, // Offset for node center
        y: event.clientY - canvasRect.top - 50,
      };

      const newNode: FlowNode = {
        id: `${draggedNodeType}-${Date.now()}`,
        type: draggedNodeType as FlowNode["type"],
        position,
        data: {
          name:
            nodePalette.find((n) => n.type === draggedNodeType)?.label ||
            "New Node",
          status: "idle",
          config: {},
        },
      };

      addNode(newNode);
      setDraggedNodeType(null);
    },
    [draggedNodeType, addNode],
  );

  // Node connection handler
  const onConnect = useCallback(
    (connection: any) => {
      const newEdge: FlowEdge = {
        id: `edge-${connection.source}-${connection.target}-${Date.now()}`,
        source: connection.source,
        target: connection.target,
        sourceHandle: connection.sourceHandle,
        targetHandle: connection.targetHandle,
        type: "custom",
        data: {
          animated: false,
          status: "idle",
        },
      };

      addEdge(newEdge);
    },
    [addEdge],
  );

  // Node click handler
  const onNodeClick = useCallback(
    (event: React.MouseEvent, node: any) => {
      selectNode(node.id);
    },
    [selectNode],
  );

  // Edge click handler
  const onEdgeClick = useCallback(
    (event: React.MouseEvent, edge: any) => {
      selectEdge(edge.id);
    },
    [selectEdge],
  );

  // Execute workflow
  const handleExecute = useCallback(async () => {
    if (!onExecute) return;

    try {
      startExecution();
      await onExecute({
        id: workflowId,
        name: workflowName,
        nodes,
        edges,
      });
    } catch (error) {
      console.error("Execution failed:", error);
      useFlowStore.getState().addLog({
        timestamp: new Date().toISOString(),
        level: "error",
        message: `Execution failed: ${error}`,
      });
    }
  }, [onExecute, startExecution, workflowId, workflowName, nodes, edges]);

  // Save workflow
  const handleSave = useCallback(async () => {
    if (!onSave) return;

    try {
      setIsSaving(true);
      await onSave({
        id: workflowId,
        name: workflowName,
        description: workflowDescription,
        nodes,
        edges,
        version: useFlowStore.getState().version + 1,
      });
      markClean();
    } catch (error) {
      console.error("Save failed:", error);
    } finally {
      setIsSaving(false);
    }
  }, [
    onSave,
    workflowId,
    workflowName,
    workflowDescription,
    nodes,
    edges,
    markClean,
  ]);

  // Filter nodes for palette
  const filteredNodes = nodePalette.filter((node) => {
    const matchesSearch =
      node.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      node.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory =
      selectedCategory === "All" || node.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = [
    "All",
    ...Array.from(new Set(nodePalette.map((n) => n.category))),
  ];

  const selectedNode = nodes.find((n) => n.id === selectedNodeId);
  const selectedEdge = edges.find((e) => e.id === selectedEdgeId);

  const isReadOnly = mode === "view";

  return (
    <div className={cn("flex h-full bg-background", className)}>
      {/* Left Sidebar - Node Palette */}
      <div
        className={cn(
          "border-r bg-card transition-all duration-300",
          sidebarCollapsed ? "w-12" : "w-80",
        )}
      >
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-4">
            <h3
              className={cn(
                "font-semibold transition-opacity",
                sidebarCollapsed ? "opacity-0" : "opacity-100",
              )}
            >
              Node Palette
            </h3>
            <Button variant="ghost" size="sm" onClick={toggleSidebar}>
              {sidebarCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </Button>
          </div>

          {!sidebarCollapsed && (
            <div className="space-y-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search nodes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select
                value={selectedCategory}
                onValueChange={setSelectedCategory}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {!sidebarCollapsed && (
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-2">
              {filteredNodes.map((nodeConfig) => {
                const Icon = nodeConfig.icon;
                return (
                  <TooltipProvider key={nodeConfig.type}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div
                          className={cn(
                            "flex items-center gap-3 p-3 rounded-lg border cursor-move transition-colors",
                            "hover:bg-accent hover:border-accent-foreground/20",
                            `border-${nodeConfig.color}-200 bg-${nodeConfig.color}-50`,
                          )}
                          draggable={!isReadOnly}
                          onDragStart={(e) => onDragStart(e, nodeConfig.type)}
                        >
                          <Icon
                            className={cn(
                              "h-5 w-5",
                              `text-${nodeConfig.color}-600`,
                            )}
                          />
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm">
                              {nodeConfig.label}
                            </div>
                            <div className="text-xs text-muted-foreground truncate">
                              {nodeConfig.description}
                            </div>
                          </div>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="right">
                        <p>{nodeConfig.description}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                );
              })}
            </div>
          </ScrollArea>
        )}
      </div>

      {/* Main Canvas Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Toolbar */}
        <div className="border-b bg-card p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Badge variant={isConnected ? "default" : "destructive"}>
                  {isConnected ? (
                    <Wifi className="h-3 w-3 mr-1" />
                  ) : (
                    <WifiOff className="h-3 w-3 mr-1" />
                  )}
                  {isConnected ? "Connected" : "Disconnected"}
                </Badge>

                {isDirty && (
                  <Badge variant="outline">
                    <Circle className="h-2 w-2 mr-1 fill-current" />
                    Unsaved
                  </Badge>
                )}

                {isSaving && (
                  <Badge variant="outline">
                    <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    Saving...
                  </Badge>
                )}

                {lastSaved && (
                  <span className="text-sm text-muted-foreground">
                    Saved {lastSaved.toLocaleTimeString()}
                  </span>
                )}
              </div>

              <Separator orientation="vertical" className="h-6" />

              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={undo}
                  disabled={!canUndo() || isReadOnly}
                >
                  <Undo2 className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={redo}
                  disabled={!canRedo() || isReadOnly}
                >
                  <Redo2 className="h-4 w-4" />
                </Button>
              </div>

              <Separator orientation="vertical" className="h-6" />

              <div className="flex items-center gap-1">
                <Button variant="ghost" size="sm" onClick={toggleGrid}>
                  <Grid
                    className={cn("h-4 w-4", gridVisible && "text-primary")}
                  />
                </Button>
                <Button variant="ghost" size="sm" onClick={toggleMiniMap}>
                  <Layers
                    className={cn("h-4 w-4", miniMapVisible && "text-primary")}
                  />
                </Button>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {!isReadOnly && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSave}
                    disabled={isSaving || !isDirty}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save
                  </Button>

                  {isExecuting ? (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={pauseExecution}
                      >
                        <Pause className="h-4 w-4 mr-2" />
                        Pause
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={stopExecution}
                      >
                        <Square className="h-4 w-4 mr-2" />
                        Stop
                      </Button>
                    </>
                  ) : (
                    <Button
                      onClick={handleExecute}
                      disabled={nodes.length <= 2} // Only start/end nodes
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Execute
                    </Button>
                  )}
                </>
              )}

              <Button variant="ghost" size="sm" onClick={toggleInspector}>
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Canvas */}
        <div className="flex-1 relative">
          <div
            ref={canvasRef}
            className="w-full h-full"
            onDrop={onDrop}
            onDragOver={onDragOver}
          >
            {/* Mock React Flow Canvas */}
            <div className="w-full h-full bg-muted/20 relative overflow-hidden">
              {/* Grid background */}
              {gridVisible && (
                <div
                  className="absolute inset-0 opacity-20"
                  style={{
                    backgroundImage: `
                      linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                      linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
                    `,
                    backgroundSize: "20px 20px",
                  }}
                />
              )}

              {/* Render edges */}
              <svg className="absolute inset-0 w-full h-full pointer-events-none">
                <defs>
                  <marker
                    id="arrowhead"
                    markerWidth="10"
                    markerHeight="7"
                    refX="9"
                    refY="3.5"
                    orient="auto"
                  >
                    <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
                  </marker>
                </defs>
                {edges.map((edge) => {
                  const sourceNode = nodes.find((n) => n.id === edge.source);
                  const targetNode = nodes.find((n) => n.id === edge.target);

                  if (!sourceNode || !targetNode) return null;

                  const sourceX = sourceNode.position.x + 128;
                  const sourceY = sourceNode.position.y + 40;
                  const targetX = targetNode.position.x + 128;
                  const targetY = targetNode.position.y + 40;

                  return (
                    <line
                      key={edge.id}
                      x1={sourceX}
                      y1={sourceY}
                      x2={targetX}
                      y2={targetY}
                      stroke={edge.selected ? "#3b82f6" : "#6b7280"}
                      strokeWidth={edge.selected ? 3 : 2}
                      markerEnd="url(#arrowhead)"
                      className="cursor-pointer"
                      style={{ pointerEvents: "stroke" }}
                      onClick={(e) => {
                        e.stopPropagation();
                        onEdgeClick(e as any, edge);
                      }}
                    />
                  );
                })}
              </svg>

              {/* Render nodes */}
              {nodes.map((node) => {
                const NodeComponent = nodeTypes[node.type];

                return (
                  <div
                    key={node.id}
                    className="absolute cursor-pointer"
                    style={{
                      left: node.position.x,
                      top: node.position.y,
                      zIndex: node.selected ? 10 : 1,
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      onNodeClick(e, node);
                    }}
                  >
                    <NodeComponent
                      id={node.id}
                      data={node.data}
                      selected={node.selected || selectedNodeId === node.id}
                    />
                  </div>
                );
              })}

              {/* Canvas click handler */}
              <div
                className="absolute inset-0 w-full h-full"
                onClick={() => {
                  selectNode(null);
                  selectEdge(null);
                }}
              />
            </div>
          </div>

          {/* Mini Map */}
          {miniMapVisible && (
            <div className="absolute bottom-4 right-4 w-48 h-32 bg-card border rounded-lg shadow-lg p-2">
              <div className="text-xs text-muted-foreground mb-1">Mini Map</div>
              <div className="w-full h-full bg-muted/50 rounded flex items-center justify-center text-xs text-muted-foreground">
                {nodes.length} nodes
              </div>
            </div>
          )}
        </div>

        {/* Bottom Logs Panel */}
        {!logsCollapsed && (
          <div className="border-t bg-card">
            <div className="flex items-center justify-between p-3 border-b">
              <div className="flex items-center gap-2">
                <Terminal className="h-4 w-4" />
                <span className="font-medium">Execution Logs</span>
                <Badge variant="outline">{executionLogs.length}</Badge>
              </div>
              <Button variant="ghost" size="sm" onClick={toggleLogs}>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </div>
            <ScrollArea className="h-48 p-3">
              <div className="space-y-1">
                {executionLogs.map((log, index) => (
                  <div key={index} className="flex items-start gap-2 text-sm">
                    <span className="text-muted-foreground text-xs font-mono">
                      {new Date(log.timestamp).toLocaleTimeString()}
                    </span>
                    <Badge
                      variant={
                        log.level === "error"
                          ? "destructive"
                          : log.level === "warn"
                            ? "secondary"
                            : "outline"
                      }
                      className="text-xs"
                    >
                      {log.level}
                    </Badge>
                    <span className="flex-1">{log.message}</span>
                    {log.nodeId && (
                      <Badge variant="outline" className="text-xs">
                        {log.nodeId}
                      </Badge>
                    )}
                  </div>
                ))}
                {executionLogs.length === 0 && (
                  <div className="text-center text-muted-foreground py-8">
                    No logs yet. Execute the workflow to see logs here.
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        )}
      </div>

      {/* Right Inspector Panel */}
      <div
        className={cn(
          "border-l bg-card transition-all duration-300",
          inspectorCollapsed ? "w-12" : "w-80",
        )}
      >
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <h3
              className={cn(
                "font-semibold transition-opacity",
                inspectorCollapsed ? "opacity-0" : "opacity-100",
              )}
            >
              Inspector
            </h3>
            <Button variant="ghost" size="sm" onClick={toggleInspector}>
              {inspectorCollapsed ? (
                <ChevronLeft className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {!inspectorCollapsed && (
          <ScrollArea className="flex-1 p-4">
            {selectedNode ? (
              <NodeInspector
                node={selectedNode}
                onUpdate={(updates) => updateNode(selectedNode.id, updates)}
                onDelete={() => deleteNode(selectedNode.id)}
                readOnly={isReadOnly}
              />
            ) : selectedEdge ? (
              <EdgeInspector
                edge={selectedEdge}
                onUpdate={(updates) => {
                  // Update edge logic here
                }}
                onDelete={() => deleteEdge(selectedEdge.id)}
                readOnly={isReadOnly}
              />
            ) : (
              <WorkflowInspector
                name={workflowName}
                description={workflowDescription}
                onUpdate={setWorkflowMetadata}
                readOnly={isReadOnly}
              />
            )}
          </ScrollArea>
        )}
      </div>

      {/* Floating Action Button for Logs */}
      {logsCollapsed && executionLogs.length > 0 && (
        <Button
          className="fixed bottom-4 left-1/2 transform -translate-x-1/2 shadow-lg"
          onClick={toggleLogs}
          variant="outline"
        >
          <Terminal className="h-4 w-4 mr-2" />
          View Logs ({executionLogs.length})
        </Button>
      )}
    </div>
  );
}

// Node Inspector Component
function NodeInspector({
  node,
  onUpdate,
  onDelete,
  readOnly,
}: {
  node: FlowNode;
  onUpdate: (updates: Partial<FlowNode["data"]>) => void;
  onDelete: () => void;
  readOnly: boolean;
}) {
  const canDelete =
    !node.id.startsWith("start-") && !node.id.startsWith("end-");

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        {node.type === "agent" && <Bot className="h-5 w-5 text-blue-600" />}
        {node.type === "tool" && <Wrench className="h-5 w-5 text-green-600" />}
        {node.type === "condition" && (
          <Diamond className="h-5 w-5 text-yellow-600" />
        )}
        {node.type === "hitl" && <User className="h-5 w-5 text-purple-600" />}
        {node.type === "start" && <Circle className="h-5 w-5 text-gray-600" />}
        {node.type === "end" && <SquareIcon className="h-5 w-5 text-red-600" />}
        <h4 className="font-medium capitalize">{node.type} Node</h4>
      </div>

      <div className="space-y-3">
        <div>
          <Label htmlFor="node-name">Name</Label>
          <Input
            id="node-name"
            value={node.data.name}
            onChange={(e) => onUpdate({ name: e.target.value })}
            disabled={readOnly}
          />
        </div>

        <div>
          <Label htmlFor="node-description">Description</Label>
          <Textarea
            id="node-description"
            value={node.data.description || ""}
            onChange={(e) => onUpdate({ description: e.target.value })}
            disabled={readOnly}
            rows={3}
          />
        </div>

        {node.data.status && (
          <div>
            <Label>Status</Label>
            <Badge
              variant={
                node.data.status === "completed"
                  ? "default"
                  : node.data.status === "failed"
                    ? "destructive"
                    : node.data.status === "running"
                      ? "secondary"
                      : "outline"
              }
              className="ml-2"
            >
              {node.data.status}
            </Badge>
          </div>
        )}

        {node.data.executionTime && (
          <div>
            <Label>Execution Time</Label>
            <div className="text-sm text-muted-foreground">
              {node.data.executionTime}ms
            </div>
          </div>
        )}

        {node.data.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{node.data.error}</AlertDescription>
          </Alert>
        )}

        {node.data.output && (
          <div>
            <Label>Output</Label>
            <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto max-h-32">
              {JSON.stringify(node.data.output, null, 2)}
            </pre>
          </div>
        )}
      </div>

      {!readOnly && canDelete && (
        <div className="pt-4 border-t">
          <Button
            variant="destructive"
            size="sm"
            onClick={onDelete}
            className="w-full"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Node
          </Button>
        </div>
      )}
    </div>
  );
}

// Edge Inspector Component
function EdgeInspector({
  edge,
  onUpdate,
  onDelete,
  readOnly,
}: {
  edge: FlowEdge;
  onUpdate: (updates: any) => void;
  onDelete: () => void;
  readOnly: boolean;
}) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <GitBranch className="h-5 w-5 text-gray-600" />
        <h4 className="font-medium">Connection</h4>
      </div>

      <div className="space-y-3">
        <div>
          <Label htmlFor="edge-label">Label</Label>
          <Input
            id="edge-label"
            value={edge.data?.label || ""}
            onChange={(e) => onUpdate({ label: e.target.value })}
            disabled={readOnly}
            placeholder="Optional label"
          />
        </div>

        <div>
          <Label htmlFor="edge-condition">Condition</Label>
          <Input
            id="edge-condition"
            value={edge.data?.condition || ""}
            onChange={(e) => onUpdate({ condition: e.target.value })}
            disabled={readOnly}
            placeholder="e.g., result === 'success'"
          />
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="edge-animated"
            checked={edge.data?.animated || false}
            onChange={(e) => onUpdate({ animated: e.target.checked })}
            disabled={readOnly}
          />
          <Label htmlFor="edge-animated">Animated</Label>
        </div>
      </div>

      {!readOnly && (
        <div className="pt-4 border-t">
          <Button
            variant="destructive"
            size="sm"
            onClick={onDelete}
            className="w-full"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Connection
          </Button>
        </div>
      )}
    </div>
  );
}

// Workflow Inspector Component
function WorkflowInspector({
  name,
  description,
  onUpdate,
  readOnly,
}: {
  name: string;
  description: string;
  onUpdate: (updates: any) => void;
  readOnly: boolean;
}) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <FileText className="h-5 w-5 text-blue-600" />
        <h4 className="font-medium">Workflow Settings</h4>
      </div>

      <div className="space-y-3">
        <div>
          <Label htmlFor="workflow-name">Name</Label>
          <Input
            id="workflow-name"
            value={name}
            onChange={(e) => onUpdate({ workflowName: e.target.value })}
            disabled={readOnly}
          />
        </div>

        <div>
          <Label htmlFor="workflow-description">Description</Label>
          <Textarea
            id="workflow-description"
            value={description}
            onChange={(e) => onUpdate({ workflowDescription: e.target.value })}
            disabled={readOnly}
            rows={4}
            placeholder="Describe what this workflow does..."
          />
        </div>
      </div>
    </div>
  );
}

// Main exported component with ReactFlow provider
export default function FlowWorkflowBuilder(props: FlowWorkflowBuilderProps) {
  return (
    <ReactFlowProvider>
      <FlowWorkflowBuilderInner {...props} />
    </ReactFlowProvider>
  );
}
