"use client";

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
  Palette,
  Settings,
  Network,
  Zap,
  Code,
  User,
  Bot,
  Wrench,
  Diamond,
  Play,
  Square,
  Circle,
  CheckCircle,
  Star,
  Sparkles,
  Eye,
  MousePointer,
  Grid,
  Move,
  RotateCcw,
  Copy,
  Trash,
  Download,
  Share,
} from 'lucide-react';

interface ShowcaseFeature {
  icon: React.ReactNode;
  title: string;
  description: string;
  highlights: string[];
  color: string;
}

const ProfessionalShowcase = () => {
  const [activeFeature, setActiveFeature] = useState(0);

  const features: ShowcaseFeature[] = [
    {
      icon: <Palette className="h-6 w-6" />,
      title: "Professional Design",
      description: "Clean, modern interface with consistent design language",
      highlights: [
        "Consistent color scheme and typography",
        "Proper spacing and visual hierarchy",
        "Responsive layout with collapsible panels",
        "Professional shadows and animations",
        "Dark/light theme support"
      ],
      color: "blue"
    },
    {
      icon: <Settings className="h-6 w-6" />,
      title: "Customizable Nodes",
      description: "Five distinct node types with full customization",
      highlights: [
        "Agent, Tool, Condition, HITL, Start/End nodes",
        "Customizable colors with preset palette",
        "Editable labels and descriptions",
        "Professional icons for each type",
        "Real-time property editing"
      ],
      color: "green"
    },
    {
      icon: <Network className="h-6 w-6" />,
      title: "Advanced Connections",
      description: "Smart connection system with visual feedback",
      highlights: [
        "Custom animated edges with styling",
        "Smart connection handles with color coding",
        "Decision nodes with Yes/No outputs",
        "Smooth bezier curves for connections",
        "Connection validation and feedback"
      ],
      color: "purple"
    },
    {
      icon: <Zap className="h-6 w-6" />,
      title: "Rich Functionality",
      description: "Comprehensive workflow building capabilities",
      highlights: [
        "Drag-and-drop from node palette",
        "Real-time property editing",
        "Node selection and manipulation",
        "Snap-to-grid for precise alignment",
        "Minimap for navigation and zoom controls"
      ],
      color: "yellow"
    },
    {
      icon: <Code className="h-6 w-6" />,
      title: "Professional Tools",
      description: "Complete toolkit for workflow management",
      highlights: [
        "Comprehensive toolbar with actions",
        "Delete and duplicate selected nodes",
        "Toggle panels for optimal workspace",
        "Save and export functionality",
        "Undo/redo operations"
      ],
      color: "red"
    },
    {
      icon: <User className="h-6 w-6" />,
      title: "HITL Integration",
      description: "Human-in-the-loop approval workflows",
      highlights: [
        "Multi-approver support with roles",
        "Form data collection and validation",
        "Escalation rules and timeouts",
        "Real-time approval status tracking",
        "Comments and feedback system"
      ],
      color: "indigo"
    }
  ];

  const nodeTypes = [
    {
      type: "start",
      icon: <Play className="h-8 w-8 text-gray-600" />,
      name: "Start Node",
      description: "Entry point for workflow execution",
      color: "gray"
    },
    {
      type: "agent",
      icon: <Bot className="h-8 w-8 text-blue-500" />,
      name: "AI Agent",
      description: "Intelligent reasoning and decision-making",
      color: "blue"
    },
    {
      type: "tool",
      icon: <Wrench className="h-8 w-8 text-green-500" />,
      name: "Tool Node",
      description: "Functional components for specific tasks",
      color: "green"
    },
    {
      type: "condition",
      icon: <Diamond className="h-8 w-8 text-yellow-500" />,
      name: "Condition",
      description: "Logic gates for workflow control",
      color: "yellow"
    },
    {
      type: "hitl",
      icon: <User className="h-8 w-8 text-purple-500" />,
      name: "HITL Node",
      description: "Human-in-the-loop approval gates",
      color: "purple"
    },
    {
      type: "end",
      icon: <Square className="h-8 w-8 text-red-500" />,
      name: "End Node",
      description: "Workflow completion point",
      color: "red"
    }
  ];

  const toolbarActions = [
    { icon: <MousePointer className="h-4 w-4" />, name: "Select", description: "Select and move nodes" },
    { icon: <Move className="h-4 w-4" />, name: "Pan", description: "Pan around the canvas" },
    { icon: <Grid className="h-4 w-4" />, name: "Grid", description: "Toggle snap-to-grid" },
    { icon: <RotateCcw className="h-4 w-4" />, name: "Undo", description: "Undo last action" },
    { icon: <Copy className="h-4 w-4" />, name: "Copy", description: "Duplicate selected nodes" },
    { icon: <Trash className="h-4 w-4" />, name: "Delete", description: "Remove selected nodes" },
    { icon: <Download className="h-4 w-4" />, name: "Export", description: "Export workflow" },
    { icon: <Share className="h-4 w-4" />, name: "Share", description: "Share workflow" },
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: "text-blue-500 bg-blue-50 border-blue-200",
      green: "text-green-500 bg-green-50 border-green-200",
      purple: "text-purple-500 bg-purple-50 border-purple-200",
      yellow: "text-yellow-500 bg-yellow-50 border-yellow-200",
      red: "text-red-500 bg-red-50 border-red-200",
      indigo: "text-indigo-500 bg-indigo-50 border-indigo-200",
      gray: "text-gray-500 bg-gray-50 border-gray-200",
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.gray;
  };

  return (
    <div className="space-y-6">
      {/* Feature Overview */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-2xl">
            <Sparkles className="h-6 w-6 text-blue-500" />
            Professional Workflow Builder
          </CardTitle>
          <CardDescription className="text-lg">
            Enterprise-grade workflow building with advanced features and professional design
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {features.map((feature, index) => (
              <Card 
                key={index}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  activeFeature === index ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => setActiveFeature(index)}
              >
                <CardContent className="p-4">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center mb-3 ${getColorClasses(feature.color)}`}>
                    {feature.icon}
                  </div>
                  <h3 className="font-semibold mb-2">{feature.title}</h3>
                  <p className="text-sm text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Feature View */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {features[activeFeature].icon}
            {features[activeFeature].title}
          </CardTitle>
          <CardDescription>
            {features[activeFeature].description}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">Key Highlights</h4>
              <ul className="space-y-2">
                {features[activeFeature].highlights.map((highlight, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{highlight}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium mb-3">Professional Benefits</h4>
              <div className="space-y-2">
                <Badge variant="outline" className="mr-2">Enterprise Ready</Badge>
                <Badge variant="outline" className="mr-2">Scalable</Badge>
                <Badge variant="outline" className="mr-2">User Friendly</Badge>
                <Badge variant="outline" className="mr-2">Customizable</Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Node Types Showcase */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>Professional Node Types</CardTitle>
          <CardDescription>
            Six distinct node types designed for comprehensive workflow building
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {nodeTypes.map((node, index) => (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4 text-center">
                  <div className={`w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-3 ${getColorClasses(node.color)}`}>
                    {node.icon}
                  </div>
                  <h4 className="font-medium mb-1">{node.name}</h4>
                  <p className="text-sm text-gray-600">{node.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Toolbar Showcase */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>Professional Toolbar</CardTitle>
          <CardDescription>
            Comprehensive set of tools for efficient workflow building
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-3">
            {toolbarActions.map((action, index) => (
              <div key={index} className="text-center p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center mx-auto mb-2">
                  {action.icon}
                </div>
                <div className="text-xs font-medium">{action.name}</div>
                <div className="text-xs text-gray-500 mt-1">{action.description}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Professional Stats */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">6</div>
              <div className="text-sm text-gray-600">Node Types</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">15+</div>
              <div className="text-sm text-gray-600">Toolbar Actions</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">100%</div>
              <div className="text-sm text-gray-600">Customizable</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-yellow-600">∞</div>
              <div className="text-sm text-gray-600">Possibilities</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfessionalShowcase;
