"use client";

import React, { memo } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "@/lib/mock-reactflow";
import { Square, CheckCircle, XCircle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface EndNodeData {
  name: string;
  status?: "idle" | "running" | "completed" | "failed";
  description?: string;
  output?: any;
  executionTime?: number;
}

const EndNode = ({ data, selected }: NodeProps<EndNodeData>) => {
  const statusColors = {
    idle: "bg-red-50 border-red-200",
    running: "bg-blue-100 border-blue-300 animate-pulse",
    completed: "bg-green-100 border-green-300",
    failed: "bg-red-100 border-red-300",
  };

  const statusColor = data.status
    ? statusColors[data.status]
    : statusColors.idle;

  const getStatusIcon = () => {
    switch (data.status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "failed":
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Square className="h-4 w-4 text-red-600" />;
    }
  };

  return (
    <Card
      className={`w-48 shadow-md transition-all border-2 ${
        selected ? "ring-2 ring-blue-500" : ""
      } ${statusColor}`}
    >
      <CardContent className="p-3">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span className="text-sm font-medium">{data.name || "End"}</span>
          </div>
          {data.status && (
            <Badge
              variant={
                data.status === "completed"
                  ? "default"
                  : data.status === "failed"
                    ? "destructive"
                    : "outline"
              }
              className="text-xs"
            >
              {data.status}
            </Badge>
          )}
        </div>

        {data.description && (
          <p className="text-xs text-muted-foreground mb-2">
            {data.description}
          </p>
        )}

        {data.executionTime && (
          <div className="text-xs text-muted-foreground">
            Completed in {data.executionTime}ms
          </div>
        )}

        {data.output && (
          <div className="mt-2 p-2 bg-muted rounded text-xs">
            <div className="font-medium mb-1">Final Output:</div>
            <div className="text-muted-foreground truncate">
              {typeof data.output === "string"
                ? data.output
                : JSON.stringify(data.output)}
            </div>
          </div>
        )}
      </CardContent>

      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(EndNode);
