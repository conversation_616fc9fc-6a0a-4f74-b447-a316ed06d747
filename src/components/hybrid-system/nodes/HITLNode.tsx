"use client";

import React, { memo } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "@/lib/mock-reactflow";
import {
  User,
  Setting<PERSON>,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface HITLNodeData {
  name: string;
  description?: string;
  status?:
    | "idle"
    | "running"
    | "completed"
    | "failed"
    | "paused"
    | "pending_approval";
  config?: {
    approvers?: string[];
    requireAllApprovers?: boolean;
    timeout?: number;
    escalationRules?: Array<{
      condition: string;
      action: string;
      delay: number;
    }>;
    instructions?: string;
    formFields?: Array<{
      name: string;
      type: "text" | "number" | "boolean" | "select";
      required: boolean;
      options?: string[];
    }>;
    enabled?: boolean;
  };
  approvalData?: {
    requestedAt?: string;
    approvedAt?: string;
    approvedBy?: string;
    rejectedAt?: string;
    rejectedBy?: string;
    comments?: string;
    formData?: Record<string, any>;
  };
  onConfigure?: (nodeId: string) => void;
}

const HITLNode = ({ id, data, selected }: NodeProps<HITLNodeData>) => {
  const statusColors = {
    idle: "bg-gray-100",
    running: "bg-purple-100 animate-pulse",
    pending_approval: "bg-yellow-100 animate-pulse",
    completed: "bg-green-100",
    failed: "bg-red-100",
    paused: "bg-yellow-100",
  };

  const statusColor = data.status
    ? statusColors[data.status]
    : statusColors.idle;

  const getStatusIcon = () => {
    switch (data.status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "failed":
        return <XCircle className="h-4 w-4 text-red-600" />;
      case "pending_approval":
        return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      case "running":
        return <Clock className="h-4 w-4 text-purple-600 animate-spin" />;
      default:
        return null;
    }
  };

  const getApprovalInfo = () => {
    if (data.approvalData?.approvedAt) {
      return {
        text: `Approved by ${data.approvalData.approvedBy}`,
        color: "text-green-600",
      };
    }
    if (data.approvalData?.rejectedAt) {
      return {
        text: `Rejected by ${data.approvalData.rejectedBy}`,
        color: "text-red-600",
      };
    }
    if (data.status === "pending_approval") {
      const approvers = data.config?.approvers || [];
      return {
        text: `Awaiting approval (${approvers.length} approver${approvers.length !== 1 ? "s" : ""})`,
        color: "text-yellow-600",
      };
    }
    return null;
  };

  const approvalInfo = getApprovalInfo();

  return (
    <Card
      className={`w-72 shadow-md transition-all ${selected ? "ring-2 ring-purple-500" : ""} ${statusColor}`}
    >
      <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <User className="h-5 w-5 text-purple-600" />
          <CardTitle className="text-sm font-medium">{data.name}</CardTitle>
        </div>
        <div className="flex items-center gap-1">
          {getStatusIcon()}
          {data.status && (
            <Badge
              variant={
                data.status === "failed"
                  ? "destructive"
                  : data.status === "completed"
                    ? "default"
                    : data.status === "pending_approval"
                      ? "secondary"
                      : "outline"
              }
              className="text-xs"
            >
              {data.status.replace("_", " ")}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-3 pt-2">
        {data.description && (
          <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
            {data.description}
          </p>
        )}

        {/* Approval Information */}
        {approvalInfo && (
          <div
            className={`text-xs ${approvalInfo.color} mb-2 flex items-center gap-1`}
          >
            <AlertCircle className="h-3 w-3" />
            <span>{approvalInfo.text}</span>
          </div>
        )}

        {/* Configuration Summary */}
        <div className="space-y-1 text-xs">
          {data.config?.approvers && data.config.approvers.length > 0 && (
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Approvers:</span>
              <Badge variant="outline" className="text-xs">
                {data.config.approvers.length}
              </Badge>
            </div>
          )}

          {data.config?.timeout && (
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Timeout:</span>
              <span className="text-xs">{data.config.timeout}min</span>
            </div>
          )}

          {data.config?.formFields && data.config.formFields.length > 0 && (
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Form Fields:</span>
              <Badge variant="outline" className="text-xs">
                {data.config.formFields.length}
              </Badge>
            </div>
          )}
        </div>

        {/* Approval Comments */}
        {data.approvalData?.comments && (
          <div className="mt-2 p-2 bg-muted rounded text-xs">
            <div className="font-medium mb-1">Comments:</div>
            <div className="text-muted-foreground">
              {data.approvalData.comments}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-between mt-3">
          <div className="flex items-center gap-1">
            {data.config?.requireAllApprovers && (
              <Badge variant="outline" className="text-xs">
                All Required
              </Badge>
            )}
            {data.config?.escalationRules &&
              data.config.escalationRules.length > 0 && (
                <Badge variant="outline" className="text-xs">
                  Auto-escalate
                </Badge>
              )}
          </div>

          {data.onConfigure && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => data.onConfigure?.(id)}
            >
              <Settings className="h-3.5 w-3.5" />
            </Button>
          )}
        </div>
      </CardContent>

      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-purple-500 border-2 border-white"
      />

      {/* Output handles for approval/rejection */}
      <Handle
        type="source"
        position={Position.Right}
        id="approved"
        style={{ top: "40%" }}
        className="w-3 h-3 bg-green-500 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="rejected"
        style={{ top: "60%" }}
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(HITLNode);
