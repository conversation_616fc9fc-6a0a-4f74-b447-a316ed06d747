"use client";

import React, { memo } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "@/lib/mock-reactflow";
import { Play, Circle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface StartNodeData {
  name: string;
  status?: "idle" | "running" | "completed" | "failed";
  description?: string;
}

const StartNode = ({ data, selected }: NodeProps<StartNodeData>) => {
  const statusColors = {
    idle: "bg-gray-100 border-gray-300",
    running: "bg-green-100 border-green-300 animate-pulse",
    completed: "bg-green-100 border-green-300",
    failed: "bg-red-100 border-red-300",
  };

  const statusColor = data.status
    ? statusColors[data.status]
    : statusColors.idle;

  return (
    <Card
      className={`w-48 shadow-md transition-all border-2 ${
        selected ? "ring-2 ring-blue-500" : ""
      } ${statusColor}`}
    >
      <CardContent className="p-3">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className="relative">
              <Circle className="h-5 w-5 text-gray-600 fill-gray-600" />
              <Play className="h-3 w-3 text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
            </div>
            <span className="text-sm font-medium">{data.name || "Start"}</span>
          </div>
          {data.status && (
            <Badge
              variant={data.status === "failed" ? "destructive" : "outline"}
              className="text-xs"
            >
              {data.status}
            </Badge>
          )}
        </div>

        {data.description && (
          <p className="text-xs text-muted-foreground">{data.description}</p>
        )}
      </CardContent>

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-gray-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(StartNode);
