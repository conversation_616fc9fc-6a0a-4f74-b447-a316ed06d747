// Production Database Layer with Multi-Tenant Support
// PostgreSQL with Prisma ORM for type-safe database operations

import { PrismaClient } from "@prisma/client";

interface DatabaseConfig {
  url: string;
  maxConnections: number;
  connectionTimeout: number;
  queryTimeout: number;
}

interface Agent {
  id: string;
  organizationId: string;
  userId: string;
  name: string;
  description: string;
  provider: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  templateId?: string;
  isPublic: boolean;
  version: string;
  status: "active" | "inactive" | "archived";
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  lastUsedAt?: Date;
  usageCount: number;
  totalCost: number;
}

interface AgentExecution {
  id: string;
  agentId: string;
  organizationId: string;
  userId: string;
  input: string;
  output: string;
  status: "pending" | "running" | "completed" | "failed";
  startedAt: Date;
  completedAt?: Date;
  duration?: number;
  cost: number;
  tokens: {
    input: number;
    output: number;
    total: number;
  };
  metadata: Record<string, any>;
  error?: string;
}

interface PromptTemplate {
  id: string;
  organizationId: string;
  userId: string;
  name: string;
  description: string;
  content: string;
  variables: Array<{
    name: string;
    type: string;
    description: string;
    required: boolean;
    defaultValue?: any;
  }>;
  category: string;
  tags: string[];
  version: string;
  isPublic: boolean;
  rating: number;
  usageCount: number;
  performance: {
    avgResponseTime: number;
    successRate: number;
    costPerUse: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

class DatabaseManager {
  private static instance: DatabaseManager;
  private prisma: PrismaClient;
  private config: DatabaseConfig;

  private constructor() {
    this.config = {
      url: process.env.DATABASE_URL || "postgresql://localhost:5432/synapseai",
      maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || "20"),
      connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || "10000"),
      queryTimeout: parseInt(process.env.DB_QUERY_TIMEOUT || "30000"),
    };

    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: this.config.url,
        },
      },
      log:
        process.env.NODE_ENV === "development"
          ? ["query", "info", "warn", "error"]
          : ["error"],
    });

    // Connection event handlers
    this.prisma.$on("query", (e) => {
      if (process.env.NODE_ENV === "development") {
        console.log("Query: " + e.query);
        console.log("Duration: " + e.duration + "ms");
      }
    });
  }

  public static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  // Agent CRUD Operations with Multi-Tenant Security
  async createAgent(
    data: Omit<
      Agent,
      "id" | "createdAt" | "updatedAt" | "usageCount" | "totalCost"
    >,
  ): Promise<Agent> {
    try {
      const agent = await this.prisma.agent.create({
        data: {
          ...data,
          usageCount: 0,
          totalCost: 0,
          status: "active",
          metadata: data.metadata || {},
        },
      });
      return agent as Agent;
    } catch (error) {
      console.error("Error creating agent:", error);
      throw new Error("Failed to create agent");
    }
  }

  async getAgent(id: string, organizationId: string): Promise<Agent | null> {
    try {
      const agent = await this.prisma.agent.findFirst({
        where: {
          id,
          organizationId, // Multi-tenant security
        },
      });
      return agent as Agent | null;
    } catch (error) {
      console.error("Error fetching agent:", error);
      throw new Error("Failed to fetch agent");
    }
  }

  async updateAgent(
    id: string,
    organizationId: string,
    data: Partial<Agent>,
  ): Promise<Agent> {
    try {
      const agent = await this.prisma.agent.update({
        where: {
          id,
          organizationId, // Multi-tenant security
        },
        data: {
          ...data,
          updatedAt: new Date(),
        },
      });
      return agent as Agent;
    } catch (error) {
      console.error("Error updating agent:", error);
      throw new Error("Failed to update agent");
    }
  }

  async deleteAgent(id: string, organizationId: string): Promise<void> {
    try {
      await this.prisma.agent.update({
        where: {
          id,
          organizationId, // Multi-tenant security
        },
        data: {
          status: "archived",
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      console.error("Error deleting agent:", error);
      throw new Error("Failed to delete agent");
    }
  }

  async listAgents(
    organizationId: string,
    filters?: {
      userId?: string;
      status?: string;
      provider?: string;
      isPublic?: boolean;
      limit?: number;
      offset?: number;
    },
  ): Promise<{ agents: Agent[]; total: number }> {
    try {
      const where = {
        organizationId, // Multi-tenant security
        ...(filters?.userId && { userId: filters.userId }),
        ...(filters?.status && { status: filters.status }),
        ...(filters?.provider && { provider: filters.provider }),
        ...(filters?.isPublic !== undefined && { isPublic: filters.isPublic }),
      };

      const [agents, total] = await Promise.all([
        this.prisma.agent.findMany({
          where,
          orderBy: { updatedAt: "desc" },
          take: filters?.limit || 50,
          skip: filters?.offset || 0,
        }),
        this.prisma.agent.count({ where }),
      ]);

      return { agents: agents as Agent[], total };
    } catch (error) {
      console.error("Error listing agents:", error);
      throw new Error("Failed to list agents");
    }
  }

  // Agent Execution Operations
  async createExecution(
    data: Omit<AgentExecution, "id" | "startedAt">,
  ): Promise<AgentExecution> {
    try {
      const execution = await this.prisma.agentExecution.create({
        data: {
          ...data,
          startedAt: new Date(),
          status: "pending",
        },
      });
      return execution as AgentExecution;
    } catch (error) {
      console.error("Error creating execution:", error);
      throw new Error("Failed to create execution");
    }
  }

  async updateExecution(
    id: string,
    organizationId: string,
    data: Partial<AgentExecution>,
  ): Promise<AgentExecution> {
    try {
      const execution = await this.prisma.agentExecution.update({
        where: {
          id,
          organizationId, // Multi-tenant security
        },
        data: {
          ...data,
          ...(data.status === "completed" && { completedAt: new Date() }),
        },
      });
      return execution as AgentExecution;
    } catch (error) {
      console.error("Error updating execution:", error);
      throw new Error("Failed to update execution");
    }
  }

  async getExecution(
    id: string,
    organizationId: string,
  ): Promise<AgentExecution | null> {
    try {
      const execution = await this.prisma.agentExecution.findFirst({
        where: {
          id,
          organizationId, // Multi-tenant security
        },
      });
      return execution as AgentExecution | null;
    } catch (error) {
      console.error("Error fetching execution:", error);
      throw new Error("Failed to fetch execution");
    }
  }

  // Prompt Template Operations
  async createTemplate(
    data: Omit<
      PromptTemplate,
      "id" | "createdAt" | "updatedAt" | "usageCount" | "rating" | "performance"
    >,
  ): Promise<PromptTemplate> {
    try {
      const template = await this.prisma.promptTemplate.create({
        data: {
          ...data,
          usageCount: 0,
          rating: 0,
          performance: {
            avgResponseTime: 0,
            successRate: 0,
            costPerUse: 0,
          },
        },
      });
      return template as PromptTemplate;
    } catch (error) {
      console.error("Error creating template:", error);
      throw new Error("Failed to create template");
    }
  }

  async getTemplate(
    id: string,
    organizationId: string,
  ): Promise<PromptTemplate | null> {
    try {
      const template = await this.prisma.promptTemplate.findFirst({
        where: {
          id,
          OR: [
            { organizationId }, // Organization templates
            { isPublic: true }, // Public templates
          ],
        },
      });
      return template as PromptTemplate | null;
    } catch (error) {
      console.error("Error fetching template:", error);
      throw new Error("Failed to fetch template");
    }
  }

  async listTemplates(
    organizationId: string,
    filters?: {
      category?: string;
      isPublic?: boolean;
      userId?: string;
      limit?: number;
      offset?: number;
    },
  ): Promise<{ templates: PromptTemplate[]; total: number }> {
    try {
      const where = {
        OR: [
          { organizationId }, // Organization templates
          { isPublic: true }, // Public templates
        ],
        ...(filters?.category && { category: filters.category }),
        ...(filters?.isPublic !== undefined && { isPublic: filters.isPublic }),
        ...(filters?.userId && { userId: filters.userId }),
      };

      const [templates, total] = await Promise.all([
        this.prisma.promptTemplate.findMany({
          where,
          orderBy: [{ rating: "desc" }, { usageCount: "desc" }],
          take: filters?.limit || 50,
          skip: filters?.offset || 0,
        }),
        this.prisma.promptTemplate.count({ where }),
      ]);

      return { templates: templates as PromptTemplate[], total };
    } catch (error) {
      console.error("Error listing templates:", error);
      throw new Error("Failed to list templates");
    }
  }

  // Analytics and Usage Tracking
  async updateAgentUsage(
    agentId: string,
    organizationId: string,
    cost: number,
  ): Promise<void> {
    try {
      await this.prisma.agent.update({
        where: {
          id: agentId,
          organizationId,
        },
        data: {
          usageCount: { increment: 1 },
          totalCost: { increment: cost },
          lastUsedAt: new Date(),
        },
      });
    } catch (error) {
      console.error("Error updating agent usage:", error);
      throw new Error("Failed to update agent usage");
    }
  }

  async updateTemplateUsage(
    templateId: string,
    organizationId: string,
  ): Promise<void> {
    try {
      await this.prisma.promptTemplate.update({
        where: {
          id: templateId,
          OR: [{ organizationId }, { isPublic: true }],
        },
        data: {
          usageCount: { increment: 1 },
        },
      });
    } catch (error) {
      console.error("Error updating template usage:", error);
      // Don't throw error for template usage updates
    }
  }

  // Health Check
  async healthCheck(): Promise<{
    status: "healthy" | "unhealthy";
    details: any;
  }> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return {
        status: "healthy",
        details: {
          database: "connected",
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        status: "unhealthy",
        details: {
          database: "disconnected",
          error: error instanceof Error ? error.message : "Unknown error",
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  // Cleanup and Connection Management
  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}

// Export singleton instance
export const db = DatabaseManager.getInstance();
export type { Agent, AgentExecution, PromptTemplate };
